// كشف الاستلامات التفاعلي - JavaScript Functions
// 🚀 بدء تحميل ملف receipts.js
console.log('🚀 تحميل ملف receipts.js...');

// العناوين الافتراضية الجديدة - فترات زمنية محددة
const DEFAULT_HEADERS = ['الرقم', 'موقع الاستلام', 'من 6 مساءً إلى 10 ليلاً', 'من 10 ليلاً إلى 2 ليلاً', 'من 2 ليلاً إلى 6 صباحاً', 'من 6 صباحاً إلى 10 صباحاً', 'من 10 صباحاً إلى 2 ظهراً', 'من 2 ظهراً إلى 6 مساءً', 'ملاحظات'];

// العناوين الافتراضية لجدول كشف استلامات الدوريات
const DEFAULT_PATROL_HEADERS = ['الرقم', 'موقع الاستلام', '6 مساءً إلى 12 ليلاً', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات'];

// العناوين الافتراضية لكشف المناوبين
const DEFAULT_SHIFTS_HEADERS = ['الرقم', 'موقع الاستلام', '2 ظهراً إلى 10 ليلاً', '10 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 2 ظهراً', 'ملاحظات المناوبين'];

// دالة لإعادة تعيين العناوين إلى الترتيب الصحيح
function resetHeadersToCorrectOrder() {
    console.log('🔄 إعادة تعيين العناوين إلى الترتيب الصحيح...');

    // إعادة تعيين عناوين جدول الدوريات
    patrolData.headers = [...DEFAULT_PATROL_HEADERS];

    // إعادة تعيين عناوين كشف المناوبين
    shiftsData.headers = [...DEFAULT_SHIFTS_HEADERS];

    console.log('✅ تم إعادة تعيين العناوين بنجاح');
    console.log('📋 عناوين الدوريات الجديدة:', patrolData.headers);
    console.log('📋 عناوين المناوبين الجديدة:', shiftsData.headers);
}

console.log('✅ تم تحديد العناوين الافتراضية');
console.log('📋 عناوين كشف الاستلامات:', DEFAULT_HEADERS);
console.log('📋 عناوين جدول الدوريات:', DEFAULT_PATROL_HEADERS);

let receiptData = {
    hijriDate: '',
    gregorianDate: '',
    receiptNumber: '',
    headers: [
        [...DEFAULT_HEADERS] // نسخة من العناوين الافتراضية
    ],
    rows: [],
    patrolNotes: [],
    guardNotes: []
};

console.log('📊 تم تهيئة بيانات كشف الاستلامات:', receiptData);

// بيانات جدول كشف استلامات الدوريات
let patrolData = {
    headers: [...DEFAULT_PATROL_HEADERS],
    rows: []
};

// بيانات كشف المناوبين
let shiftsData = {
    headers: [...DEFAULT_SHIFTS_HEADERS],
    rows: []
};

console.log('📊 تم تهيئة بيانات جدول الدوريات:', patrolData);
console.log('📊 تم تهيئة بيانات كشف المناوبين:', shiftsData);

// قاعدة بيانات الأفراد (سيتم تحميلها من الخادم)
let personnelDatabase = [];

// تحميل بيانات الأفراد من الخادم
async function loadPersonnelDatabase() {
    try {
        const response = await fetch('/personnel/api/list');
        if (response.ok) {
            const data = await response.json();
            personnelDatabase = data.map(person => ({
                nationalId: person.national_id,
                name: person.name,
                rank: person.rank || 'غير محدد',
                unit: person.unit || 'غير محدد'
            }));
            console.log('تم تحميل بيانات الأفراد:', personnelDatabase.length, 'فرد');
        } else {
            console.error('خطأ في تحميل بيانات الأفراد:', response.status);
        }
    } catch (error) {
        console.error('خطأ في الاتصال بخادم الأفراد:', error);
    }
}

// مواقع من إدارة المواقع
let locationsDatabase = [];

// متغيرات للحفظ التلقائي
let autoSaveTimeout = null;
const AUTO_SAVE_DELAY = 2000; // حفظ تلقائي بعد ثانيتين من آخر تغيير

// متغيرات لتتبع مهل الحفظ (debouncing)
let saveTimeouts = {
    patrol: null,
    shifts: null,
    locations: null,
    patrolLocations: null,
    shiftsLocations: null
};

// متغيرات للحفظ
let isSaving = false;
const SAVE_DELAY = 1000; // تأخير الحفظ بثانية واحدة

// دالة الحفظ التلقائي مع تأخير
function scheduleAutoSave() {
    // إلغاء أي حفظ سابق مجدول
    if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
    }

    // جدولة حفظ جديد
    autoSaveTimeout = setTimeout(() => {
        console.log('🔄 بدء الحفظ التلقائي...');
        performAutoSave();
    }, AUTO_SAVE_DELAY);

    console.log('⏰ تم جدولة الحفظ التلقائي خلال', AUTO_SAVE_DELAY / 1000, 'ثانية');
}

// حفظ بيانات جدول الدوريات مع debouncing
function savePatrolDataToServer() {
    // إلغاء أي حفظ سابق
    if (saveTimeouts.patrol) {
        clearTimeout(saveTimeouts.patrol);
    }

    // تعيين مهلة جديدة
    saveTimeouts.patrol = setTimeout(() => {
        try {
            console.log('💾 حفظ بيانات جدول الدوريات في الخادم...');

            // تنظيف البيانات قبل الحفظ
            const cleanedPatrolData = cleanDataBeforeSave(patrolData);

            fetch('/receipts/api/save-patrol-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(cleanedPatrolData)
            })
            .then(response => {
                console.log('📡 استجابة حفظ بيانات الدوريات:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('✅ تم حفظ بيانات جدول الدوريات في الخادم:', data);
            })
            .catch(error => {
                console.error('❌ خطأ في حفظ بيانات الدوريات:', error);
            });
        } catch (error) {
            console.error('❌ خطأ في حفظ بيانات الدوريات:', error);
        }
        saveTimeouts.patrol = null;
    }, 300); // 300ms debounce
}

// حفظ بيانات كشف المناوبين مع debouncing
function saveShiftsDataToServer() {
    // إلغاء أي حفظ سابق
    if (saveTimeouts.shifts) {
        clearTimeout(saveTimeouts.shifts);
    }

    // تعيين مهلة جديدة
    saveTimeouts.shifts = setTimeout(() => {
        try {
            console.log('💾 حفظ بيانات كشف المناوبين في الخادم...');

            // تنظيف البيانات قبل الحفظ
            const cleanedShiftsData = cleanDataBeforeSave(shiftsData);

            fetch('/receipts/api/save-shifts-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(cleanedShiftsData)
            })
            .then(response => {
                console.log('📡 استجابة حفظ بيانات المناوبين:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('✅ تم حفظ بيانات كشف المناوبين في الخادم:', data);
            })
            .catch(error => {
                console.error('❌ خطأ في حفظ بيانات المناوبين:', error);
            });
        } catch (error) {
            console.error('❌ خطأ في حفظ بيانات المناوبين:', error);
        }
        saveTimeouts.shifts = null;
    }, 300); // 300ms debounce
}









// حفظ المواقع في الخادم مع تأخير
function saveLocationsToServer() {
    // إلغاء أي حفظ سابق
    if (saveTimeouts.locations) {
        clearTimeout(saveTimeouts.locations);
    }

    // تأخير الحفظ
    saveTimeouts.locations = setTimeout(() => {
        try {
            if (isSaving) {
                console.log('⏳ جاري الحفظ، تجاهل الطلب الجديد');
                return;
            }

            const currentLocations = {};
            const locationSelects = document.querySelectorAll('.location-select');
            let hasLocations = false;

            locationSelects.forEach((select, index) => {
                if (select.value && select.value !== '') {
                    currentLocations[index] = select.value;
                    hasLocations = true;
                }
            });

            // لا نحفظ إذا لم تكن هناك مواقع محددة
            if (!hasLocations) {
                console.log('ℹ️ لا توجد مواقع لحفظها');
                return;
            }

            isSaving = true;
            console.log('💾 حفظ المواقع في الخادم...');

            return fetch('/receipts/api/save-receipt-locations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    locations: currentLocations,
                    timestamp: new Date().toISOString()
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('✅ تم حفظ المواقع بنجاح');
                return data;
            })
            .catch(error => {
                console.error('❌ خطأ في حفظ المواقع:', error);
                throw error;
            })
            .finally(() => {
                isSaving = false;
            });
        } catch (error) {
            console.error('❌ خطأ في حفظ المواقع:', error);
            isSaving = false;
            return Promise.reject(error);
        }
    }, SAVE_DELAY);
}

// تحميل المواقع من الخادم
function loadLocationsFromServer() {
    console.log('🔄 بدء تحميل المواقع من الخادم...');
    return fetch('/receipts/api/get-receipt-locations')
        .then(response => {
            console.log('📡 استجابة الخادم:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('📦 بيانات المواقع المستلمة:', data);
            if (data.success && data.locations && Object.keys(data.locations).length > 0) {
                console.log('✅ تم تحميل المواقع من الخادم:', data.locations);

                // تطبيق المواقع على الجدول فوراً
                applyLocationsToTable(data.locations);

                // تطبيق المواقع على البيانات أيضاً
                Object.keys(data.locations).forEach(rowIndex => {
                    const index = parseInt(rowIndex);
                    if (receiptData.rows[index]) {
                        receiptData.rows[index][1] = data.locations[rowIndex];
                        console.log(`💾 تم تحديث البيانات للصف ${index}: ${data.locations[rowIndex]}`);
                    }
                });

                return data.locations;
            } else {
                console.log('ℹ️ لا توجد مواقع محفوظة أو فشل في التحميل');
                return {};
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل المواقع:', error);
            return {};
        });
}

// تطبيق المواقع على الجدول
function applyLocationsToTable(locations) {
    const locationSelects = document.querySelectorAll('.location-select');
    console.log('🎯 عدد القوائم المنسدلة الموجودة:', locationSelects.length);
    console.log('📍 المواقع المراد تطبيقها:', locations);

    locationSelects.forEach((select, index) => {
        const locationId = locations[index.toString()];
        if (locationId) {
            console.log(`📍 تطبيق الموقع ${locationId} على الصف ${index}`);
            select.value = locationId;

            // التأكد من أن الخيار موجود في القائمة
            const option = select.querySelector(`option[value="${locationId}"]`);
            if (option) {
                option.selected = true;
                console.log(`✅ تم تحديد الموقع "${option.textContent}" للصف ${index}`);
            } else {
                console.warn(`⚠️ الموقع ${locationId} غير موجود في القائمة للصف ${index}`);
            }
        }
    });
}

// حفظ جميع البيانات في الخادم (للحفظ التلقائي - بدون إشعارات)
function saveAllDataToServer() {
    try {
        console.log('💾 حفظ جميع البيانات في الخادم (تلقائي)...');

        // دمج بيانات كشف الاستلامات مع بيانات جدول الدوريات وجدول المناوبين
        const allData = {
            ...receiptData,
            patrolTableData: patrolData, // إضافة بيانات جدول الدوريات
            shiftsTableData: shiftsData  // إضافة بيانات جدول المناوبين
        };

        // حفظ البيانات الرئيسية
        const savePromises = [
            fetch('/receipts/api/save-receipt-data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(allData)
            }),
            fetch('/receipts/api/save-patrol-data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(patrolData)
            }),
            fetch('/receipts/api/save-shifts-data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(shiftsData)
            })
        ];

        // حفظ المواقع أيضاً
        const currentLocations = {};
        const locationSelects = document.querySelectorAll('.location-select');
        locationSelects.forEach((select, index) => {
            if (select.value && select.value !== '') {
                currentLocations[index] = select.value;
            }
        });

        if (Object.keys(currentLocations).length > 0) {
            savePromises.push(
                fetch('/receipts/api/save-receipt-locations', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        locations: currentLocations,
                        timestamp: new Date().toISOString()
                    })
                })
            );
        }

        Promise.all(savePromises)
            .then(responses => {
                console.log('✅ تم حفظ جميع البيانات بنجاح (تلقائي)');
                // لا يوجد إشعار للحفظ التلقائي

                // حفظ إضافي للتأكد
                savePatrolDataToServer();
                saveShiftsDataToServer();
            })
            .catch(error => {
                console.error('❌ خطأ في حفظ البيانات:', error);
            });

    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات:', error);
    }
}

// حفظ جميع البيانات مع إشعار (للحفظ اليدوي)
function saveAllDataToServerWithNotification() {
    try {
        console.log('💾 حفظ جميع البيانات في الخادم (يدوي)...');

        // دمج بيانات كشف الاستلامات مع بيانات جدول الدوريات وجدول المناوبين
        const allData = {
            ...receiptData,
            patrolTableData: patrolData, // إضافة بيانات جدول الدوريات
            shiftsTableData: shiftsData  // إضافة بيانات جدول المناوبين
        };

        // حفظ البيانات الرئيسية
        const savePromises = [
            fetch('/receipts/api/save-receipt-data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(allData)
            }),
            fetch('/receipts/api/save-patrol-data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(patrolData)
            }),
            fetch('/receipts/api/save-shifts-data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(shiftsData)
            })
        ];

        // حفظ المواقع أيضاً
        const currentLocations = {};
        const locationSelects = document.querySelectorAll('.location-select');
        locationSelects.forEach((select, index) => {
            if (select.value && select.value !== '') {
                currentLocations[index] = select.value;
            }
        });

        if (Object.keys(currentLocations).length > 0) {
            savePromises.push(
                fetch('/receipts/api/save-receipt-locations', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        locations: currentLocations,
                        timestamp: new Date().toISOString()
                    })
                })
            );
        }

        return Promise.all(savePromises)
            .then(responses => {
                console.log('✅ تم حفظ جميع البيانات بنجاح (يدوي)');

                // حفظ إضافي للتأكد
                savePatrolDataToServer();
                saveShiftsDataToServer();

                return responses;
            })
            .catch(error => {
                console.error('❌ خطأ في حفظ البيانات:', error);
                throw error;
            });

    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات:', error);
        throw error;
    }
}





// تحميل جميع البيانات من الخادم
function loadAllDataFromServer() {
    console.log('📥 تحميل جميع البيانات من الخادم...');
    try {
        // تحميل بيانات كشف الاستلامات
        fetch('/receipts/api/get-receipt-data')
        .then(response => {
            console.log('📡 استجابة تحميل البيانات:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📦 بيانات مستلمة من الخادم:', data);
            if (data.success && data.data) {
                console.log('✅ تم تحميل البيانات من الخادم');

                // تحديث البيانات
                receiptData = { ...receiptData, ...data.data };

                // إذا كانت البيانات تحتوي على بيانات جدول الدوريات، استخدمها
                if (data.data.patrolTableData) {
                    patrolData = { ...patrolData, ...data.data.patrolTableData };
                    console.log('📊 تم تحميل بيانات جدول الدوريات من البيانات المدمجة');
                }

                // إذا كانت البيانات تحتوي على بيانات جدول المناوبين، استخدمها
                if (data.data.shiftsTableData) {
                    shiftsData = { ...shiftsData, ...data.data.shiftsTableData };
                    console.log('📊 تم تحميل بيانات جدول المناوبين من البيانات المدمجة');
                }

                console.log('📊 البيانات المحدثة:', receiptData);

                // تحديث النموذج
                if (receiptData.hijriDate && document.getElementById('hijriDate')) {
                    document.getElementById('hijriDate').value = receiptData.hijriDate;
                    console.log('📅 تم تحديث التاريخ الهجري');
                }
                if (receiptData.gregorianDate && document.getElementById('gregorianDate')) {
                    document.getElementById('gregorianDate').value = receiptData.gregorianDate;
                    console.log('📅 تم تحديث التاريخ الميلادي');
                }
                if (receiptData.receiptNumber && document.getElementById('receiptNumber')) {
                    document.getElementById('receiptNumber').value = receiptData.receiptNumber;
                    console.log('🔢 تم تحديث رقم الكشف');
                }

                // إعادة إنشاء الجداول
                console.log('🔄 إعادة إنشاء الجداول...');
                generateTable();
                generatePatrolTable();
                generateShiftsTable();

                // استعادة المواقع والبيانات
                setTimeout(() => {
                    console.log('🔄 استعادة المواقع والبيانات...');
                    const locationSelects = document.querySelectorAll('.location-select:not(.patrol-location-select)');
                    console.log(`🎯 عدد القوائم المنسدلة: ${locationSelects.length}`);
                    console.log(`📊 عدد الصفوف في البيانات: ${receiptData.rows.length}`);

                    locationSelects.forEach((select, index) => {
                        if (receiptData.rows[index] && receiptData.rows[index][1]) {
                            select.value = receiptData.rows[index][1];
                            console.log(`📍 تم استعادة الموقع ${receiptData.rows[index][1]} للصف ${index}`);
                        }
                    });

                    // استعادة مواقع جدول الدوريات
                    const patrolLocationSelects = document.querySelectorAll('.patrol-location-select');
                    patrolLocationSelects.forEach((select, index) => {
                        if (patrolData.rows[index] && patrolData.rows[index][1]) {
                            select.value = patrolData.rows[index][1];
                            console.log(`📍 تم استعادة موقع الدورية ${patrolData.rows[index][1]} للصف ${index}`);
                        }
                    });

                    // استعادة مواقع جدول المناوبين
                    const shiftsLocationSelects = document.querySelectorAll('.shifts-location-select');
                    shiftsLocationSelects.forEach((select, index) => {
                        if (shiftsData.rows[index] && shiftsData.rows[index][1]) {
                            select.value = shiftsData.rows[index][1];
                            console.log(`📍 تم استعادة موقع المناوبة ${shiftsData.rows[index][1]} للصف ${index}`);
                        }
                    });

                    // تحديث محتوى الخلايا أيضاً
                    const editableCells = document.querySelectorAll('.editable-cell');
                    console.log(`📝 عدد الخلايا القابلة للتحرير: ${editableCells.length}`);

                    editableCells.forEach((cell, cellIndex) => {
                        const rowIndex = Math.floor(cellIndex / (receiptData.headers[0].length - 1));
                        const colIndex = (cellIndex % (receiptData.headers[0].length - 1)) + 1;

                        if (receiptData.rows[rowIndex] && receiptData.rows[rowIndex][colIndex]) {
                            cell.textContent = receiptData.rows[rowIndex][colIndex];
                            console.log(`📝 تم استعادة النص "${receiptData.rows[rowIndex][colIndex]}" للخلية [${rowIndex}, ${colIndex}]`);
                        }
                    });

                    console.log('✅ تم استعادة جميع البيانات بنجاح');
                }, 500);

            } else {
                console.log('ℹ️ لا توجد بيانات محفوظة في الخادم');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل البيانات:', error);
        });

        // البيانات تم تحميلها بالفعل من البيانات المدمجة

    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
    }
}

// تحميل بيانات جدول ملاحظات الدوريات من الخادم
function loadPatrolDataFromServer() {
    console.log('📥 تحميل بيانات جدول الدوريات من الخادم...');
    try {
        fetch('/receipts/api/get-patrol-data')
        .then(response => {
            console.log('📡 استجابة تحميل بيانات الدوريات:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📦 بيانات الدوريات مستلمة من الخادم:', data);
            if (data.success && data.data) {
                console.log('✅ تم تحميل بيانات الدوريات من الخادم');

                // تحديث بيانات الدوريات
                patrolData = { ...patrolData, ...data.data };
                console.log('📊 بيانات الدوريات المحدثة:', patrolData);

                // إعادة إنشاء جدول الدوريات
                generatePatrolTable();

                // استعادة مواقع الدوريات والبيانات
                setTimeout(() => {
                    const patrolLocationSelects = document.querySelectorAll('.patrol-location-select');
                    patrolLocationSelects.forEach((select, index) => {
                        if (patrolData.rows[index] && patrolData.rows[index][1]) {
                            select.value = patrolData.rows[index][1];
                            console.log(`📍 تم استعادة موقع الدورية ${patrolData.rows[index][1]} للصف ${index}`);
                        }
                    });

                    // استعادة البيانات النصية بشكل مستقر
                    restoreDataStably();
                }, 200);

            } else {
                console.log('ℹ️ لا توجد بيانات دوريات محفوظة في الخادم');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل بيانات الدوريات:', error);
        });
    } catch (error) {
        console.error('❌ خطأ في تحميل بيانات الدوريات:', error);
    }
}

// تحميل بيانات جدول المناوبين من الخادم
function loadShiftsDataFromServer() {
    console.log('📥 تحميل بيانات جدول المناوبين من الخادم...');
    try {
        fetch('/receipts/api/get-shifts-data')
        .then(response => {
            console.log('📡 استجابة تحميل بيانات المناوبين:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('📦 بيانات المناوبين مستلمة من الخادم:', data);
            if (data.success && data.shiftsData) {
                console.log('✅ تم تحميل بيانات المناوبين من الخادم');

                // تحديث بيانات المناوبين
                shiftsData = { ...shiftsData, ...data.shiftsData };
                console.log('📊 بيانات المناوبين المحدثة:', shiftsData);

                // إعادة إنشاء جدول المناوبين
                generateShiftsTable();

                // استعادة مواقع المناوبين والبيانات
                setTimeout(() => {
                    const shiftsLocationSelects = document.querySelectorAll('.shifts-location-select');
                    shiftsLocationSelects.forEach((select, index) => {
                        if (shiftsData.rows[index] && shiftsData.rows[index][1]) {
                            select.value = shiftsData.rows[index][1];
                            console.log(`📍 تم استعادة موقع المناوبة ${shiftsData.rows[index][1]} للصف ${index}`);
                        }
                    });

                    // استعادة البيانات النصية بشكل مستقر
                    restoreDataStably();
                }, 200);

            } else {
                console.log('ℹ️ لا توجد بيانات مناوبين محفوظة في الخادم');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل بيانات المناوبين:', error);
        });
    } catch (error) {
        console.error('❌ خطأ في تحميل بيانات المناوبين:', error);
    }
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تهيئة الصفحة...');
    console.log('🔍 فحص العناصر المطلوبة...');

    // فحص وجود العناصر المطلوبة
    const requiredElements = ['tableHeader', 'receiptTableBody', 'patrolTableHeader', 'patrolTableBody'];
    let missingElements = [];

    requiredElements.forEach(id => {
        const element = document.getElementById(id);
        if (!element) {
            missingElements.push(id);
            console.error(`❌ العنصر ${id} غير موجود`);
        } else {
            console.log(`✅ العنصر ${id} موجود`);
        }
    });

    if (missingElements.length > 0) {
        console.error('❌ عناصر مفقودة:', missingElements);
        // تم إزالة رسالة الخطأ لتجنب الإزعاج
        return;
    }

    console.log('✅ جميع العناصر المطلوبة موجودة');

    // إخفاء أي رسائل خطأ قد تكون موجودة
    hideAllAlerts();

    // إزالة إشعار الباركود نهائياً
    removeBarcodeNotifications();

    // بدء مراقبة رسائل الخطأ
    startErrorMonitoring();

    initializeReceipt();
    loadPersonnelDatabase(); // تحميل بيانات الأفراد

    // تحميل المواقع من إدارة المواقع أولاً
    loadLocationsFromManagement().then(() => {
        console.log('🔧 بدء إنشاء الجداول...');
        console.log('📊 بيانات كشف الاستلامات:', receiptData);
        console.log('📊 بيانات جدول الدوريات:', patrolData);

        // بعد تحميل قائمة المواقع، إنشاء الجداول
        console.log('🔨 إنشاء جدول كشف الاستلامات...');
        generateTable();

        console.log('🔨 إنشاء جدول كشف استلامات الدوريات...');
        generatePatrolTable();

        console.log('🔨 إنشاء جدول المناوبين...');
        generateShiftsTable();

        // ثم تحميل المواقع المحفوظة من الخادم
        setTimeout(() => {
            loadLocationsFromServer();
            loadPatrolLocationsFromServer();
            loadShiftsLocationsFromServer();
        }, 300);

        console.log('✅ تم إنشاء الجداول بنجاح');
    });

    loadReceiptData(); // تحميل البيانات المحفوظة

    // تحميل بيانات الجداول من قاعدة البيانات
    setTimeout(() => {
        loadAllDataFromServer(); // تحميل جميع البيانات من قاعدة البيانات
        loadPatrolDataFromServer(); // تحميل بيانات الدوريات
        loadShiftsDataFromServer(); // تحميل بيانات المناوبين (سيقوم بإنشاء الجدول تلقائياً)

        // إعادة إنشاء جدول الدوريات فقط (المناوبين يتم إنشاؤه في loadShiftsDataFromServer)
        setTimeout(() => {
            generatePatrolTable();
            // تحميل إضافي للتأكد من استعادة بيانات المناوبين
            setTimeout(() => {
                reapplyShiftsDataToInputs();
            }, 200);
        }, 500);
    }, 1000);

    setupEventListeners();

    // إعداد مستمعي أحداث للحفظ الفوري
    setTimeout(() => {
        setupInputEventListeners();
    }, 100);

    // فرض تحديث التاريخ الهجري التلقائي بعد تحميل البيانات
    setTimeout(async () => {
        const hijriElement = document.getElementById('hijriDate');
        if (hijriElement) {
            const currentHijri = await getCurrentHijriDate();
            hijriElement.value = currentHijri;
            console.log('✅ تم فرض تحديث التاريخ الهجري التلقائي إلى:', currentHijri);
        }

        // إزالة إشعارات الباركود مرة أخرى للتأكد
        removeBarcodeNotifications();
    }, 500);
});

// تهيئة كشف الاستلامات باستخدام API
async function initializeReceipt() {
    try {
        // الحصول على التاريخ والوقت من API
        const response = await fetch('/reports/api/hijri-date');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                // تحديث اليوم
                document.getElementById('dayName').value = data.day_name;

                // تحديث التاريخ الميلادي
                document.getElementById('gregorianDate').value = data.gregorian_formatted;

                // تحديث التاريخ الهجري
                document.getElementById('hijriDate').value = data.hijri_formatted;

                // رقم الكشف التلقائي
                document.getElementById('receiptNumber').value = generateReceiptNumber();

                console.log('✅ تم تهيئة كشف الاستلامات من API:', {
                    dayName: data.day_name,
                    gregorianDate: data.gregorian_formatted,
                    hijriDate: data.hijri_formatted
                });
            } else {
                // في حالة فشل API، استخدم الطريقة التقليدية
                initializeReceiptManually();
            }
        } else {
            // في حالة فشل API، استخدم الطريقة التقليدية
            initializeReceiptManually();
        }

    } catch (error) {
        console.error('خطأ في تهيئة كشف الاستلامات من API:', error);
        initializeReceiptManually();
    }
}

// تهيئة كشف الاستلامات يدوياً (كبديل)
function initializeReceiptManually() {
    const now = new Date();

    // تحديث اليوم
    const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    const dayName = dayNames[now.getDay()];
    document.getElementById('dayName').value = dayName;

    // تحديث التاريخ الميلادي
    const gregorianDate = now.toISOString().split('T')[0];
    document.getElementById('gregorianDate').value = gregorianDate;

    // تحديث التاريخ الهجري التلقائي
    getCurrentHijriDate().then(hijriDate => {
        document.getElementById('hijriDate').value = hijriDate;
    });

    // رقم الكشف التلقائي
    document.getElementById('receiptNumber').value = generateReceiptNumber();

    // التأكد من أن العناوين تستخدم القيم الافتراضية
    if (receiptData.headers[0].length === 0 || receiptData.headers[0][0] === '') {
        receiptData.headers[0] = [...DEFAULT_HEADERS];
    }

    // فرض استخدام العناوين الافتراضية الجديدة
    receiptData.headers[0] = [...DEFAULT_HEADERS];

    // فرض استخدام العناوين الافتراضية الجديدة لجدول ملاحظات الدوريات
    patrolData.headers = [...DEFAULT_PATROL_HEADERS];

    // فرض استخدام العناوين الافتراضية الجديدة لجدول المناوبين
    shiftsData.headers = [...DEFAULT_SHIFTS_HEADERS];

    // تهيئة البيانات الافتراضية
    if (receiptData.rows.length === 0) {
        for (let i = 1; i <= 18; i++) {
            receiptData.rows.push(Array(receiptData.headers[0].length).fill(''));
        }
    }

    // تهيئة البيانات الافتراضية لجدول ملاحظات الدوريات
    if (patrolData.rows.length === 0) {
        for (let i = 0; i < 6; i++) {
            patrolData.rows.push(Array(patrolData.headers.length).fill(''));
        }
    }

    // تهيئة البيانات الافتراضية لجدول المناوبين (صف واحد فقط)
    if (shiftsData.rows.length === 0) {
        shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));
    }

    console.log('📊 بيانات جدول المناوبين بعد التهيئة:', shiftsData);
}

// تحويل التاريخ إلى هجري باستخدام API (دقيق ومعتمد على تقويم أم القرى)
async function convertToHijri(date) {
    try {
        // إذا كان التاريخ هو اليوم، استخدم API للحصول على التاريخ الحالي
        const today = new Date();
        const isToday = date.toDateString() === today.toDateString();

        if (isToday) {
            return await getCurrentHijriDate();
        }

        // للتواريخ الأخرى، استخدم حساب تقريبي مؤقت
        // (يمكن تحسينه لاحقاً بإضافة API للتواريخ المحددة)
        const response = await fetch('/reports/api/hijri-date');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                // حساب الفرق بالأيام
                const currentDate = new Date(data.gregorian_formatted);
                const daysDiff = Math.floor((date - currentDate) / (1000 * 60 * 60 * 24));

                if (daysDiff === 0) {
                    return data.hijri_formatted;
                }

                // للتواريخ الأخرى، استخدم حساب تقريبي
                const hijriMonths = [
                    'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
                    'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
                ];

                let hijriDay = data.hijri_day + daysDiff;
                let hijriMonth = data.hijri_month;
                let hijriYear = data.hijri_year;

                // تعديل بسيط للشهر والسنة
                while (hijriDay > 30) {
                    hijriDay -= 30;
                    hijriMonth++;
                    if (hijriMonth > 12) {
                        hijriMonth = 1;
                        hijriYear++;
                    }
                }

                while (hijriDay < 1) {
                    hijriDay += 30;
                    hijriMonth--;
                    if (hijriMonth < 1) {
                        hijriMonth = 12;
                        hijriYear--;
                    }
                }

                return `${hijriDay.toString().padStart(2, '0')} ${hijriMonths[hijriMonth - 1]} ${hijriYear}هـ`;
            }
        }

        // في حالة فشل API، استخدم حساب بديل
        return fallbackHijriCalculation(date);

    } catch (error) {
        console.error('خطأ في تحويل التاريخ الهجري:', error);
        return fallbackHijriCalculation(date);
    }
}

// حساب بديل للتاريخ الهجري
function fallbackHijriCalculation(date) {
    const baseGregorianDate = new Date('2025-06-14');
    const baseHijriDay = 18;
    const baseHijriMonth = 12;
    const baseHijriYear = 1446;

    const daysDiff = Math.floor((date - baseGregorianDate) / (1000 * 60 * 60 * 24));

    const hijriMonths = [
        'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
        'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
    ];

    let hijriDay = baseHijriDay + daysDiff;
    let hijriMonth = baseHijriMonth;
    let hijriYear = baseHijriYear;

    while (hijriDay > 30) {
        hijriDay -= 30;
        hijriMonth++;
        if (hijriMonth > 12) {
            hijriMonth = 1;
            hijriYear++;
        }
    }

    while (hijriDay < 1) {
        hijriDay += 30;
        hijriMonth--;
        if (hijriMonth < 1) {
            hijriMonth = 12;
            hijriYear--;
        }
    }

    return `${hijriDay.toString().padStart(2, '0')} ${hijriMonths[hijriMonth - 1]} ${hijriYear}هـ`;
}

// الحصول على التاريخ الهجري الحالي من API
async function getCurrentHijriDate() {
    try {
        const response = await fetch('/reports/api/hijri-date');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                console.log('✅ تم الحصول على التاريخ الهجري من API:', data.hijri_formatted);
                return data.hijri_formatted;
            }
        }
        throw new Error('فشل في الحصول على التاريخ من API');
    } catch (error) {
        console.error('خطأ في الحصول على التاريخ الهجري:', error);
        // استخدم الحساب البديل
        return fallbackHijriCalculation(new Date());
    }
}

// توليد رقم كشف تلقائي
function generateReceiptNumber() {
    const now = new Date();
    return `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
}

// تحديث التواريخ تلقائياً باستخدام API
async function updateDatesAutomatically() {
    try {
        // الحصول على التاريخ والوقت من API
        const response = await fetch('/reports/api/hijri-date');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                // تحديث اليوم
                const dayElement = document.getElementById('dayName');
                if (dayElement) {
                    dayElement.value = data.day_name;
                }

                // تحديث التاريخ الميلادي
                const gregorianElement = document.getElementById('gregorianDate');
                if (gregorianElement) {
                    gregorianElement.value = data.gregorian_formatted;
                }

                // تحديث التاريخ الهجري
                const hijriElement = document.getElementById('hijriDate');
                if (hijriElement) {
                    hijriElement.value = data.hijri_formatted;
                }

                console.log('✅ تم تحديث التواريخ تلقائياً من API:', {
                    dayName: data.day_name,
                    gregorianDate: data.gregorian_formatted,
                    hijriDate: data.hijri_formatted
                });

                return;
            }
        }

        // في حالة فشل API، استخدم الطريقة التقليدية
        updateDatesManually();

    } catch (error) {
        console.error('خطأ في تحديث التواريخ من API:', error);
        updateDatesManually();
    }
}

// تحديث التواريخ يدوياً (كبديل)
function updateDatesManually() {
    const now = new Date();

    // تحديث اليوم
    const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    const dayName = dayNames[now.getDay()];
    const dayElement = document.getElementById('dayName');
    if (dayElement) {
        dayElement.value = dayName;
    }

    // تحديث التاريخ الميلادي
    const gregorianDate = now.toISOString().split('T')[0];
    const gregorianElement = document.getElementById('gregorianDate');
    if (gregorianElement) {
        gregorianElement.value = gregorianDate;
    }

    // تحديث التاريخ الهجري
    getCurrentHijriDate().then(hijriDate => {
        const hijriElement = document.getElementById('hijriDate');
        if (hijriElement) {
            hijriElement.value = hijriDate;
        }
    });

    console.log('تم تحديث التواريخ يدوياً:', { dayName, gregorianDate });
}

// تحميل المواقع من إدارة المواقع
function loadLocationsFromManagement() {
    return fetch('/locations/api/list')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                locationsDatabase = data.locations.map(location => ({
                    id: location.id,
                    name: location.name,
                    type: location.type || 'عام'
                }));

                console.log('تم تحميل المواقع من إدارة المواقع:', locationsDatabase.length);
                updateLocationSelects();
                // تم إزالة الإشعار لتجنب الإزعاج
                return Promise.resolve();
            } else {
                console.error('خطأ في تحميل المواقع:', data.message);
                // استخدام مواقع افتراضية في حالة الخطأ
                loadDefaultLocations();
                return Promise.resolve();
            }
        })
        .catch(error => {
            console.error('خطأ في الاتصال بإدارة المواقع:', error);
            // استخدام مواقع افتراضية في حالة الخطأ
            loadDefaultLocations();
            return Promise.resolve();
        });
}

// تحميل مواقع افتراضية في حالة عدم توفر إدارة المواقع
function loadDefaultLocations() {
    locationsDatabase = [
        { id: 1, name: 'البوابة الرئيسية الشمالية', type: 'أمني' },
        { id: 2, name: 'البوابة الجنوبية', type: 'أمني' },
        { id: 3, name: 'برج المراقبة الشرقي', type: 'مراقبة' },
        { id: 4, name: 'المبنى الإداري', type: 'إداري' },
        { id: 5, name: 'موقف السيارات', type: 'حراسة' }
    ];
    updateLocationSelects();
    console.log('تم تحميل المواقع الافتراضية');
}

// تحديث القوائم المنسدلة للمواقع
function updateLocationSelects() {
    // تحديث مواقع كشف الاستلامات
    const locationSelects = document.querySelectorAll('.location-select:not(.patrol-location-select)');
    locationSelects.forEach(select => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">اختر الموقع</option>';

        locationsDatabase.forEach(location => {
            const option = document.createElement('option');
            option.value = location.id;
            option.textContent = location.name;
            if (location.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    });

    // تحديث مواقع جدول ملاحظات الدوريات
    const patrolLocationSelects = document.querySelectorAll('.patrol-location-select');
    patrolLocationSelects.forEach(select => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">اختر الموقع</option>';

        locationsDatabase.forEach(location => {
            const option = document.createElement('option');
            option.value = location.id;
            option.textContent = location.name;
            if (location.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    });

    // تحديث مواقع جدول المناوبين
    updateShiftsLocationSelects();
}

// إنشاء الجدول التفاعلي
function generateTable() {
    generateTableHeader();
    generateTableBody();

    // تحميل المواقع المحفوظة بعد إنشاء الجدول
    setTimeout(() => {
        loadLocationsFromServer();
    }, 100);
}

// إنشاء رأس الجدول
function generateTableHeader() {
    const thead = document.getElementById('tableHeader');
    if (!thead) {
        console.error('❌ عنصر tableHeader غير موجود');
        return;
    }

    if (!receiptData.headers || !receiptData.headers[0] || receiptData.headers[0].length === 0) {
        console.error('❌ عناوين الجدول غير محددة');
        receiptData.headers = [DEFAULT_HEADERS];
    }

    thead.innerHTML = '';

    const headerRow = document.createElement('tr');

    receiptData.headers[0].forEach((headerText, index) => {
        const th = document.createElement('th');
        th.className = 'text-center align-middle';

        if (index === 0) {
            // عمود الرقم - غير قابل للتحرير
            th.innerHTML = headerText;
            th.className += ' row-number';
        } else {
            // العناوين القابلة للتحرير
            th.innerHTML = `
                <input type="text" class="editable-header" value="${headerText}"
                       onchange="updateHeader(${index}, this.value)"
                       onblur="updateHeader(${index}, this.value)">
                <div class="column-controls">
                    <button type="button" class="control-btn btn-add" onclick="addColumnAfter(${index})" title="إضافة عمود">
                        <i class="fas fa-plus"></i>
                    </button>
                    ${receiptData.headers[0].length > 3 ? `
                    <button type="button" class="control-btn btn-delete" onclick="deleteColumn(${index})" title="حذف العمود">
                        <i class="fas fa-times"></i>
                    </button>
                    ` : ''}
                </div>
            `;
        }

        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
}

// إنشاء محتوى الجدول
function generateTableBody() {
    const tbody = document.getElementById('receiptTableBody');
    if (!tbody) {
        console.error('❌ عنصر receiptTableBody غير موجود');
        return;
    }

    if (!receiptData.rows || receiptData.rows.length === 0) {
        console.warn('⚠️ صفوف الجدول فارغة، إنشاء صفوف افتراضية');
        receiptData.rows = [];
        for (let i = 1; i <= 18; i++) {
            receiptData.rows.push(Array(receiptData.headers[0].length).fill(''));
        }
    }

    tbody.innerHTML = '';

    receiptData.rows.forEach((rowData, rowIndex) => {
        const row = document.createElement('tr');

        rowData.forEach((cellData, cellIndex) => {
            const td = document.createElement('td');
            td.className = 'text-center align-middle';

            if (cellIndex === 0) {
                // عمود الرقم
                td.innerHTML = `
                    <span class="row-number">${rowIndex + 1}</span>
                    <div class="row-controls">
                        <button type="button" class="control-btn btn-add" onclick="addRowAfter(${rowIndex})" title="إضافة صف">
                            <i class="fas fa-plus"></i>
                        </button>
                        ${receiptData.rows.length > 1 ? `
                        <button type="button" class="control-btn btn-delete" onclick="deleteRow(${rowIndex})" title="حذف الصف">
                            <i class="fas fa-times"></i>
                        </button>
                        ` : ''}
                    </div>
                `;
                td.className += ' row-number-cell';
            } else if (cellIndex === 1) {
                // عمود الموقع - قائمة منسدلة
                td.innerHTML = `
                    <select class="location-select" onchange="updateCell(${rowIndex}, ${cellIndex}, this.value); saveLocationsToServer(); autoSave();">
                        <option value="">اختر الموقع</option>
                    </select>
                `;

                // تحميل المواقع
                const select = td.querySelector('.location-select');
                locationsDatabase.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    if (location.id == cellData) {
                        option.selected = true;
                    }
                    select.appendChild(option);
                });
            } else {
                // الخلايا العادية القابلة للتحرير
                td.innerHTML = `
                    <input type="text" class="editable-cell" value="${cellData}"
                           onchange="updateCell(${rowIndex}, ${cellIndex}, this.value)"
                           onblur="updateCell(${rowIndex}, ${cellIndex}, this.value)"
                           oninput="debouncedAutoSearchPersonnel(this, this.value)">
                `;
            }

            row.appendChild(td);
        });

        tbody.appendChild(row);
    });
}

// تحديث عنوان العمود
function updateHeader(columnIndex, newValue) {
    receiptData.headers[0][columnIndex] = newValue;
    console.log(`تم تحديث عنوان العمود ${columnIndex} إلى: ${newValue}`);

    // حفظ محلي فقط
    localStorage.setItem('receiptData', JSON.stringify(receiptData));

    // جدولة حفظ تلقائي في قاعدة البيانات
    scheduleAutoSave();
}

// تحديث عنوان العمود (للتوافق مع الكود الجديد)
function updateColumnTitle(columnIndex, newValue) {
    if (receiptData.columns && receiptData.columns[columnIndex]) {
        receiptData.columns[columnIndex].title = newValue;
    } else if (receiptData.headers && receiptData.headers[0]) {
        receiptData.headers[0][columnIndex] = newValue;
    }
    console.log(`تم تحديث عنوان العمود ${columnIndex} إلى: ${newValue}`);

    // حفظ تلقائي بعد التحديث
    autoSave();
}

// تحديث خلية
function updateCell(rowIndex, columnIndex, newValue) {
    if (!receiptData.rows[rowIndex]) {
        receiptData.rows[rowIndex] = Array(receiptData.headers[0].length).fill('');
    }

    // البحث التلقائي عن الأفراد إذا كانت القيمة رقم هوية (10 أرقام)
    if (newValue && newValue.length === 10 && /^\d+$/.test(newValue)) {
        const personnel = personnelDatabase.find(p => p.nationalId === newValue);
        if (personnel) {
            newValue = personnel.name;
            // تحديث الخلية في الواجهة
            const cell = document.querySelector(`#receiptTable tbody tr:nth-child(${rowIndex + 1}) td:nth-child(${columnIndex + 1}) input`);
            if (cell) {
                cell.value = personnel.name;
            }
        }
    }

    receiptData.rows[rowIndex][columnIndex] = newValue;
    console.log(`تم تحديث الخلية [${rowIndex}, ${columnIndex}] إلى: ${newValue}`);

    // إذا كان التحديث في عمود الموقع (العمود الثاني)، طباعة رسالة
    if (columnIndex === 1) {
        console.log(`🏢 تم تحديث الموقع في الصف ${rowIndex} إلى: ${newValue}`);
    }

    // حفظ محلي فقط
    localStorage.setItem('receiptData', JSON.stringify(receiptData));

    // جدولة حفظ تلقائي في قاعدة البيانات
    scheduleAutoSave();
}

// تحديث بيانات الخلية (للتوافق مع الكود الجديد)
function updateCellData(rowIndex, columnId, newValue) {
    // البحث التلقائي عن الأفراد إذا كانت القيمة رقم هوية (10 أرقام)
    if (newValue && newValue.length === 10 && /^\d+$/.test(newValue)) {
        const personnel = personnelDatabase.find(p => p.nationalId === newValue);
        if (personnel) {
            newValue = personnel.name;
            // تحديث الخلية في الواجهة
            const cell = event.target;
            if (cell) {
                cell.textContent = personnel.name;
            }
        }
    }

    // البحث عن فهرس العمود
    const columnIndex = receiptData.columns ? receiptData.columns.findIndex(col => col.id === columnId) : -1;

    if (columnIndex !== -1) {
        // إذا كان هناك نظام أعمدة جديد
        if (!receiptData.rows[rowIndex]) {
            receiptData.rows[rowIndex] = {};
        }
        receiptData.rows[rowIndex][columnId] = newValue;
    } else {
        // النظام القديم
        if (!receiptData.rows[rowIndex]) {
            receiptData.rows[rowIndex] = Array(receiptData.headers[0].length).fill('');
        }
        // محاولة تحديد الفهرس من columnId
        const index = parseInt(columnId) || 0;
        receiptData.rows[rowIndex][index] = newValue;
    }

    console.log(`تم تحديث بيانات الخلية [${rowIndex}, ${columnId}] إلى: ${newValue}`);

    // حفظ تلقائي بعد التحديث
    autoSave();
}

// إضافة عمود جديد
function addColumn() {
    receiptData.headers[0].push('عمود جديد');

    // إضافة خلية فارغة لكل صف
    receiptData.rows.forEach(row => {
        row.push('');
    });

    generateTable();
    autoSave(); // حفظ تلقائي بعد الإضافة
    scheduleAutoSave(); // حفظ في قاعدة البيانات
}

// إضافة عمود بعد عمود محدد
function addColumnAfter(columnIndex) {
    receiptData.headers[0].splice(columnIndex + 1, 0, 'عمود جديد');

    // إضافة خلية فارغة لكل صف
    receiptData.rows.forEach(row => {
        row.splice(columnIndex + 1, 0, '');
    });

    generateTable();
    autoSave(); // حفظ تلقائي بعد الإضافة
    scheduleAutoSave(); // حفظ في قاعدة البيانات
}

// حذف عمود
function deleteColumn(columnIndex) {
    if (receiptData.headers[0].length <= 3) {
        return;
    }

    if (confirm('هل أنت متأكد من حذف هذا العمود؟')) {
        receiptData.headers[0].splice(columnIndex, 1);

        // حذف الخلايا المقابلة من كل صف
        receiptData.rows.forEach(row => {
            row.splice(columnIndex, 1);
        });

        generateTable();
        autoSave(); // حفظ تلقائي بعد الحذف
        scheduleAutoSave(); // حفظ في قاعدة البيانات
    }
}

// إضافة صف جديد
function addRow() {
    const newRow = Array(receiptData.headers[0].length).fill('');
    receiptData.rows.push(newRow);

    generateTable();
    autoSave(); // حفظ تلقائي بعد الإضافة
    scheduleAutoSave(); // حفظ في قاعدة البيانات
}

// إضافة صف بعد صف محدد
function addRowAfter(rowIndex) {
    const newRow = Array(receiptData.headers[0].length).fill('');
    receiptData.rows.splice(rowIndex + 1, 0, newRow);
    generateTable();
    autoSave(); // حفظ تلقائي بعد الإضافة
}

// حذف صف
function deleteRow(rowIndex) {
    if (receiptData.rows.length <= 1) {
        showAlert('لا يمكن حذف الصف الوحيد المتبقي', 'warning');
        return;
    }

    if (confirm('هل أنت متأكد من حذف هذا الصف؟')) {
        receiptData.rows.splice(rowIndex, 1);
        generateTable();
        autoSave(); // حفظ تلقائي بعد الحذف
        showAlert('تم حذف الصف بنجاح', 'success');
    }
}

// تفريغ الكشف مع الاحتفاظ بالعناوين والمواقع
function resetHeaders() {
    if (confirm('هل أنت متأكد من تفريغ الكشف؟ سيتم الاحتفاظ بأوقات الاستلامات والمواقع وحذف البيانات الأخرى فقط.')) {
        // حفظ المواقع الحالية قبل التفريغ
        const currentLocations = [];
        const locationSelects = document.querySelectorAll('.location-select');
        locationSelects.forEach((select, index) => {
            if (select.value) {
                currentLocations[index] = select.value;
            }
        });

        // تفريغ جميع الصفوف من البيانات مع الاحتفاظ بالمواقع
        receiptData.rows.forEach((row, index) => {
            // تحديث طول الصف ليطابق العناوين
            while (row.length < receiptData.headers[0].length) {
                row.push('');
            }
            while (row.length > receiptData.headers[0].length) {
                row.pop();
            }

            // تفريغ البيانات مع الاحتفاظ بالمواقع
            for (let i = 0; i < row.length; i++) {
                if (i === 1) {
                    // الاحتفاظ بالموقع في العمود الثاني
                    if (currentLocations[index] !== undefined) {
                        row[i] = currentLocations[index];
                    }
                } else {
                    // تفريغ باقي الخلايا
                    row[i] = '';
                }
            }
        });

        // إعادة تعيين التواريخ ورقم الكشف
        document.getElementById('hijriDate').value = '';
        document.getElementById('gregorianDate').value = '';
        document.getElementById('receiptNumber').value = generateReceiptNumber();

        // تفريغ جدول ملاحظات الدوريات أيضاً
        patrolData.rows = [];
        for (let i = 0; i < 6; i++) {
            patrolData.rows.push(Array(patrolData.headers.length).fill(''));
        }

        generateTable();
        generatePatrolTable();

        // استعادة المواقع بعد إنشاء الجدول
        setTimeout(() => {
            const newLocationSelects = document.querySelectorAll('.location-select');
            newLocationSelects.forEach((select, index) => {
                if (currentLocations[index]) {
                    select.value = currentLocations[index];
                }
            });
        }, 100);

        autoSave();
        showAlert('تم تفريغ الكشف مع الاحتفاظ بأوقات الاستلامات والمواقع', 'success');
    }
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث عن الأفراد بالهوية الوطنية
    if (document.getElementById('searchNationalId')) {
        document.getElementById('searchNationalId').addEventListener('input', function() {
            searchPersonnelLive(this.value);
        });
    }

    // تحديث التاريخ الهجري عند تغيير التاريخ الميلادي
    document.getElementById('gregorianDate').addEventListener('change', async function() {
        const date = new Date(this.value);
        const hijriDate = await convertToHijri(date);
        document.getElementById('hijriDate').value = hijriDate;
        autoSave(); // حفظ تلقائي عند تغيير التاريخ
    });

    // حفظ تلقائي عند تغيير التاريخ الهجري
    document.getElementById('hijriDate').addEventListener('input', function() {
        autoSave();
    });

    // حفظ تلقائي عند تغيير رقم الكشف
    document.getElementById('receiptNumber').addEventListener('input', function() {
        autoSave();
    });

    // حفظ تلقائي عند تغيير ملاحظات الدوريات
    if (document.getElementById('patrolNotes')) {
        document.getElementById('patrolNotes').addEventListener('input', function() {
            autoSave();
        });
    }

    // حفظ تلقائي عند تغيير ملاحظات الحراس
    if (document.getElementById('guardNotes')) {
        document.getElementById('guardNotes').addEventListener('input', function() {
            autoSave();
        });
    }

    // حفظ تلقائي كل 30 ثانية
    setInterval(autoSave, 30000);

    // حفظ إضافي في قاعدة البيانات كل دقيقة
    setInterval(() => {
        console.log('💾 حفظ دوري في قاعدة البيانات...');

        // جمع البيانات من الخانات قبل الحفظ
        collectPatrolDataFromInputs();
        collectShiftsDataFromInputs();

        saveAllDataToServer();
        savePatrolDataToServer();
        saveShiftsDataToServer();
    }, 60000);

    // إضافة مستمعي أحداث لجدول ملاحظات الدوريات
    setupPatrolTableEventListeners();

    // تحديث التواريخ تلقائياً كل دقيقة
    setInterval(updateDatesAutomatically, 60000);

    // تحديث التاريخ الهجري كل ساعة للتأكد من الدقة
    setInterval(async () => {
        const hijriElement = document.getElementById('hijriDate');
        if (hijriElement) {
            const currentHijri = await getCurrentHijriDate();
            hijriElement.value = currentHijri;
            console.log('🕐 تحديث تلقائي للتاريخ الهجري:', currentHijri);
        }
    }, 3600000); // كل ساعة

    // تحديث التاريخ الهجري عند منتصف الليل (تغيير اليوم)
    function scheduleNextMidnightUpdate() {
        const now = new Date();
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 1, 0); // منتصف الليل + ثانية واحدة

        const timeUntilMidnight = tomorrow.getTime() - now.getTime();

        setTimeout(async () => {
            console.log('🌙 تحديث التاريخ الهجري عند منتصف الليل');
            await updateDatesAutomatically();

            // جدولة التحديث التالي
            scheduleNextMidnightUpdate();
        }, timeUntilMidnight);

        console.log(`⏰ تم جدولة تحديث التاريخ الهجري عند منتصف الليل خلال ${Math.round(timeUntilMidnight / 1000 / 60)} دقيقة`);
    }

    // بدء جدولة تحديث منتصف الليل
    scheduleNextMidnightUpdate();

    // إزالة إشعار الباركود مرة واحدة بعد التحميل
    setTimeout(() => {
        removeBarcodeNotifications();
    }, 1000);

    // فرض تحديث التاريخ الهجري التلقائي فور إعداد المستمعين
    setTimeout(async () => {
        const hijriElement = document.getElementById('hijriDate');
        if (hijriElement) {
            const currentHijri = await getCurrentHijriDate();
            hijriElement.value = currentHijri;
            console.log('✅ تم فرض التاريخ الهجري التلقائي من setupEventListeners:', currentHijri);
        }
    }, 200);

    // حفظ المواقع عند إغلاق الصفحة أو تحديثها
    window.addEventListener('beforeunload', function(e) {
        saveLocationsToServer();
        savePatrolLocationsToServer();
        saveShiftsLocationsToServer();
        autoSave();

        // حفظ فوري في قاعدة البيانات
        saveAllDataToServer();
        savePatrolDataToServer();
        saveShiftsDataToServer();
    });

    // حفظ المواقع عند فقدان التركيز على الصفحة
    window.addEventListener('blur', function() {
        // جمع البيانات من الخانات قبل الحفظ
        collectPatrolDataFromInputs();
        collectShiftsDataFromInputs();

        saveLocationsToServer();
        savePatrolLocationsToServer();
        saveShiftsLocationsToServer();

        // حفظ فوري في قاعدة البيانات
        saveAllDataToServer();
        savePatrolDataToServer();
        saveShiftsDataToServer();
    });

    // حفظ المواقع كل دقيقة للتأكد
    setInterval(() => {
        saveLocationsToServer();
        savePatrolLocationsToServer();
        saveShiftsLocationsToServer();
    }, 60000);
}

// إعداد مستمعي الأحداث لجدول ملاحظات الدوريات
function setupPatrolTableEventListeners() {
    console.log('🔧 إعداد مستمعي الأحداث لجدول ملاحظات الدوريات...');

    // إضافة مستمع أحداث للجدول بأكمله لالتقاط التغييرات
    const patrolTableBody = document.getElementById('patrolTableBody');
    if (patrolTableBody) {
        // مستمع للتغييرات في الخلايا
        patrolTableBody.addEventListener('input', function(e) {
            if (e.target.classList.contains('patrol-editable-cell')) {
                console.log('📝 تم تحديث خلية في جدول الدوريات:', e.target.value);
                autoSave();
            }
        });

        // مستمع للتغييرات في القوائم المنسدلة
        patrolTableBody.addEventListener('change', function(e) {
            if (e.target.classList.contains('patrol-location-select')) {
                console.log('📍 تم تحديث موقع في جدول الدوريات:', e.target.value);
                autoSave();
            }
        });

        // مستمع لفقدان التركيز
        patrolTableBody.addEventListener('blur', function(e) {
            if (e.target.classList.contains('patrol-editable-cell')) {
                console.log('👁️ فقدان التركيز من خلية في جدول الدوريات');
                autoSave();
            }
        }, true);

        console.log('✅ تم إعداد مستمعي الأحداث لجدول ملاحظات الدوريات');
    } else {
        console.warn('⚠️ لم يتم العثور على عنصر patrolTableBody');
    }
}

// تحديث موقع الصف (للتوافق مع الكود القديم)
function updateRowLocation(selectElement, rowNumber) {
    const locationId = selectElement.value;
    const location = locationsDatabase.find(loc => loc.id == locationId);

    if (location) {
        console.log(`تم تحديد الموقع "${location.name}" للصف ${rowNumber}`);
    }
}

// عرض رسالة تنبيه محسنة (فقط للرسائل الإيجابية)
function showAlert(message, type = 'info', duration = 5000) {
    // منع عرض رسائل الخطأ والتحذير
    if (type === 'error' || type === 'danger' || type === 'warning') {
        console.log('تم منع عرض رسالة:', message);
        return;
    }

    // إزالة أي إشعارات سابقة
    const existingAlerts = document.querySelectorAll('.custom-alert');
    existingAlerts.forEach(alert => alert.remove());

    const alert = document.createElement('div');
    alert.className = 'custom-alert';

    // تحديد الألوان والأيقونات حسب النوع
    let bgColor, textColor, borderColor, icon;
    if (type === 'success') {
        bgColor = '#d4edda';
        textColor = '#155724';
        borderColor = '#c3e6cb';
        icon = 'fas fa-check-circle';
    } else {
        bgColor = '#d1ecf1';
        textColor = '#0c5460';
        borderColor = '#bee5eb';
        icon = 'fas fa-info-circle';
    }

    // تطبيق التصميم المحسن
    alert.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        min-width: 350px;
        max-width: 500px;
        padding: 16px 20px;
        background-color: ${bgColor};
        color: ${textColor};
        border: 1px solid ${borderColor};
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 14px;
        line-height: 1.5;
        direction: rtl;
        text-align: right;
        animation: slideInRight 0.3s ease-out;
        backdrop-filter: blur(10px);
        border-left: 4px solid ${type === 'success' ? '#28a745' : '#17a2b8'};
    `;

    alert.innerHTML = `
        <div style="display: flex; align-items: center; gap: 12px;">
            <i class="${icon}" style="font-size: 18px; color: ${type === 'success' ? '#28a745' : '#17a2b8'};"></i>
            <span style="flex: 1; font-weight: 500;">${message}</span>
            <button type="button" style="
                background: none;
                border: none;
                color: ${textColor};
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                margin: 0;
                opacity: 0.7;
                transition: opacity 0.2s;
            " onclick="this.parentElement.parentElement.remove();">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // إضافة CSS للأنيميشن إذا لم يكن موجوداً
    if (!document.getElementById('alert-animations')) {
        const style = document.createElement('style');
        style.id = 'alert-animations';
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
            .custom-alert:hover button {
                opacity: 1 !important;
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(alert);

    // إزالة التنبيه تلقائياً بعد المدة المحددة مع أنيميشن
    setTimeout(() => {
        if (alert.parentNode) {
            alert.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 300);
        }
    }, duration);
}

// إخفاء جميع رسائل التنبيه
function hideAllAlerts() {
    // إخفاء جميع عناصر التنبيه الموجودة
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        if (alert.classList.contains('alert-danger') ||
            alert.classList.contains('alert-error') ||
            alert.textContent.includes('خطأ') ||
            alert.textContent.includes('error')) {
            alert.style.display = 'none';
            alert.remove();
        }
    });

    // إخفاء أي عناصر خطأ أخرى
    const errorElements = document.querySelectorAll('[class*="error"], [class*="danger"], [style*="red"]');
    errorElements.forEach(element => {
        if (element.textContent.includes('خطأ') || element.textContent.includes('error')) {
            element.style.display = 'none';
        }
    });

    console.log('تم إخفاء جميع رسائل الخطأ');
}

// إزالة إشعار الباركود المحدد من صفحة كشف الاستلامات
function removeBarcodeNotifications() {
    console.log('🧹 إزالة إشعار الباركود من كشف الاستلامات...');

    // النص المحدد المطلوب إزالته
    const specificBarcodeText = 'لتسجيل المستلم يمكنك مسح باركود رقم الهوية الوطنية';

    // إزالة من searchResults تحديداً
    const searchResults = document.getElementById('searchResults');
    if (searchResults) {
        // البحث عن العناصر التي تحتوي على النص المحدد
        const alerts = searchResults.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (alert.textContent.includes(specificBarcodeText)) {
                console.log('🗑️ تم إزالة إشعار الباركود:', alert.textContent);
                alert.remove();
            }
        });

        // إذا كان searchResults يحتوي على النص مباشرة
        if (searchResults.textContent.includes(specificBarcodeText)) {
            searchResults.innerHTML = '';
            console.log('✅ تم تنظيف searchResults من إشعار الباركود');
        }
    }

    // إزالة أي عنصر آخر يحتوي على هذا النص المحدد
    document.querySelectorAll('.alert, .notification, .message').forEach(element => {
        if (element.textContent.includes(specificBarcodeText)) {
            console.log('🗑️ تم إزالة عنصر يحتوي على إشعار الباركود:', element.textContent);
            element.remove();
        }
    });

    console.log('✅ تم الانتهاء من إزالة إشعار الباركود المحدد');
}

// مراقبة ومنع ظهور رسائل خطأ جديدة
function startErrorMonitoring() {
    // مراقبة إضافة عناصر جديدة للصفحة
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1) { // عنصر HTML
                    // فحص إذا كان العنصر رسالة خطأ
                    if (node.classList && (
                        node.classList.contains('alert-danger') ||
                        node.classList.contains('alert-error') ||
                        node.textContent.includes('خطأ') ||
                        node.textContent.includes('error')
                    )) {
                        console.log('تم منع عرض رسالة خطأ:', node.textContent);
                        node.style.display = 'none';
                        node.remove();
                    }

                    // فحص العناصر الفرعية أيضاً
                    const errorChildren = node.querySelectorAll && node.querySelectorAll('.alert-danger, .alert-error, [class*="error"], [class*="danger"]');
                    if (errorChildren) {
                        errorChildren.forEach(child => {
                            if (child.textContent.includes('خطأ') || child.textContent.includes('error')) {
                                console.log('تم منع عرض رسالة خطأ فرعية:', child.textContent);
                                child.style.display = 'none';
                                child.remove();
                            }
                        });
                    }

                    // فحص وإزالة إشعار الباركود المحدد
                    const specificBarcodeText = 'لتسجيل المستلم يمكنك مسح باركود رقم الهوية الوطنية';
                    if (node.textContent && node.textContent.includes(specificBarcodeText)) {
                        console.log('تم منع عرض إشعار الباركود المحدد:', node.textContent);
                        node.style.display = 'none';
                        node.remove();
                    }
                }
            });
        });
    });

    // بدء المراقبة
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    console.log('تم بدء مراقبة رسائل الخطأ');
}

// البحث المباشر عن الأفراد
async function searchPersonnelLive(nationalId) {
    const resultsDiv = document.getElementById('searchResults');
    const detailsDiv = document.getElementById('personnelDetails');
    const insertBtn = document.getElementById('insertPersonnelBtn');

    // إزالة المسافات والتأكد من أن القيمة نص
    nationalId = String(nationalId).trim();

    if (nationalId.length === 0) {
        resultsDiv.innerHTML = '';
        detailsDiv.style.display = 'none';
        insertBtn.disabled = true;
        return;
    }

    if (nationalId.length < 10) {
        resultsDiv.innerHTML = `
            <div class="alert alert-warning text-center" style="
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 8px;
                padding: 20px;
                margin: 10px 0;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                border-right: 4px solid #ffc107;
            ">
                <i class="fas fa-exclamation-triangle" style="font-size: 24px; color: #ffc107; margin-bottom: 10px;"></i>
                <div style="font-weight: 500; color: #856404;">رقم الهوية يجب أن يكون 10 أرقام (تم إدخال ${nationalId.length} أرقام)</div>
            </div>
        `;
        detailsDiv.style.display = 'none';
        insertBtn.disabled = true;
        return;
    }

    // عرض رسالة البحث
    resultsDiv.innerHTML = `
        <div class="alert alert-info text-center" style="
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-right: 4px solid #17a2b8;
        ">
            <i class="fas fa-spinner fa-spin" style="font-size: 24px; color: #17a2b8; margin-bottom: 10px;"></i>
            <div style="font-weight: 500; color: #0c5460;">جاري البحث عن الفرد...</div>
        </div>
    `;

    // البحث في قاعدة البيانات المحلية أولاً
    let personnel = personnelDatabase.find(p => p.nationalId === nationalId);

    // إذا لم يتم العثور على الفرد، جرب البحث في الخادم
    if (!personnel) {
        try {
            const response = await fetch(`/receipts/personnel/search?national_id=${nationalId}`);
            if (response.ok) {
                const data = await response.json();
                if (data && data.success && data.personnel) {
                    personnel = {
                        nationalId: data.personnel.national_id,
                        name: data.personnel.name,
                        rank: data.personnel.rank || 'غير محدد',
                        unit: data.personnel.unit || 'غير محدد'
                    };
                    // إضافة الفرد إلى قاعدة البيانات المحلية
                    personnelDatabase.push(personnel);
                }
            }
        } catch (error) {
            console.error('خطأ في البحث عن الفرد:', error);
        }
    }

    if (personnel) {
        resultsDiv.innerHTML = `
            <div class="alert alert-success text-center" style="
                background-color: #d4edda;
                border: 1px solid #c3e6cb;
                border-radius: 8px;
                padding: 20px;
                margin: 10px 0;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                border-right: 4px solid #28a745;
            ">
                <i class="fas fa-check-circle" style="font-size: 24px; color: #28a745; margin-bottom: 10px;"></i>
                <div style="font-weight: 500; color: #155724;">تم العثور على الفرد بنجاح</div>
            </div>
        `;

        // عرض تفاصيل الفرد
        document.getElementById('detailName').textContent = personnel.name;
        document.getElementById('detailRank').textContent = personnel.rank;
        document.getElementById('detailUnit').textContent = personnel.unit;
        document.getElementById('detailNationalId').textContent = personnel.nationalId;

        detailsDiv.style.display = 'block';
        insertBtn.disabled = false;
        insertBtn.setAttribute('data-personnel', JSON.stringify(personnel));
    } else {
        resultsDiv.innerHTML = `
            <div class="alert alert-danger text-center" style="
                background-color: #f8d7da;
                border: 1px solid #f5c6cb;
                border-radius: 8px;
                padding: 20px;
                margin: 10px 0;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                border-right: 4px solid #dc3545;
            ">
                <i class="fas fa-exclamation-circle" style="font-size: 24px; color: #dc3545; margin-bottom: 10px;"></i>
                <div style="font-weight: 500; color: #721c24;">لم يتم العثور على فرد بهذا الرقم</div>
            </div>
        `;
        detailsDiv.style.display = 'none';
        insertBtn.disabled = true;
    }
}

// البحث عن الأفراد (زر البحث)
function searchPersonnel() {
    const nationalId = document.getElementById('searchNationalId').value;
    searchPersonnelLive(nationalId);
}

// حفظ فرد جديد
function savePersonnel() {
    const nationalId = document.getElementById('nationalId').value;
    const fullName = document.getElementById('fullName').value;
    const rank = document.getElementById('rank').value;
    const unit = document.getElementById('unit').value;

    if (!nationalId || !fullName) {
        console.log('يرجى إدخال رقم الهوية والاسم الكامل');
        return;
    }

    // التحقق من عدم وجود الفرد مسبقاً
    const existingPersonnel = personnelDatabase.find(p => p.nationalId === nationalId);
    if (existingPersonnel) {
        console.log('هذا الفرد موجود مسبقاً في النظام');
        return;
    }

    // إضافة الفرد الجديد
    const newPersonnel = {
        nationalId: nationalId,
        name: fullName,
        rank: rank,
        unit: unit
    };

    personnelDatabase.push(newPersonnel);

    showAlert('✅ تم إضافة الفرد بنجاح إلى قاعدة البيانات', 'success');

    // إغلاق النافذة وإعادة تعيين النموذج
    const modal = bootstrap.Modal.getInstance(document.getElementById('addPersonnelModal'));
    modal.hide();
    document.getElementById('personnelForm').reset();
}

// حفظ موقع جديد
function saveLocation() {
    const locationName = document.getElementById('locationName').value;
    const locationType = document.getElementById('locationType').value;
    const locationDescription = document.getElementById('locationDescription').value;

    if (!locationName) {
        console.log('يرجى إدخال اسم الموقع');
        return;
    }

    // إضافة الموقع الجديد
    const newLocation = {
        id: locationsDatabase.length + 1,
        name: locationName,
        type: locationType,
        description: locationDescription
    };

    locationsDatabase.push(newLocation);

    // تحديث القوائم المنسدلة
    generateTable();

    showAlert('✅ تم إضافة الموقع بنجاح إلى قاعدة البيانات', 'success');

    // إغلاق النافذة وإعادة تعيين النموذج
    const modal = bootstrap.Modal.getInstance(document.getElementById('addLocationModal'));
    modal.hide();
    document.getElementById('locationForm').reset();
}

// عرض الأعمدة
function renderColumns() {
    const headerRow = document.getElementById('tableHeader');
    headerRow.innerHTML = '<th class="row-number-header">#</th>';
    
    receiptData.columns.forEach((column, index) => {
        const th = document.createElement('th');
        th.innerHTML = `
            <div class="column-title" contenteditable="true" onblur="updateColumnTitle(${index}, this.textContent)">${column.title}</div>
            <div class="column-controls">
                <button type="button" class="control-btn btn-add" onclick="addColumn(${index})" title="إضافة عمود">
                    <i class="fas fa-plus"></i>
                </button>
                <button type="button" class="control-btn btn-delete" onclick="deleteColumn(${index})" title="حذف العمود">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        headerRow.appendChild(th);
    });
}

// عرض الصفوف
function renderRows() {
    const tbody = document.getElementById('tableBody');
    tbody.innerHTML = '';
    
    receiptData.rows.forEach((row, rowIndex) => {
        const tr = document.createElement('tr');
        
        // رقم الصف
        const rowNumberTd = document.createElement('td');
        rowNumberTd.className = 'row-number';
        rowNumberTd.innerHTML = `
            <span class="row-number">${String(rowIndex + 1).padStart(2, '0')}</span>
            <div class="row-controls">
                <button type="button" class="control-btn btn-add" onclick="addRowAfter(${rowIndex})" title="إضافة صف">
                    <i class="fas fa-plus"></i>
                </button>
                ${receiptData.rows.length > 1 ? `
                <button type="button" class="control-btn btn-delete" onclick="deleteRow(${rowIndex})" title="حذف الصف">
                    <i class="fas fa-times"></i>
                </button>
                ` : ''}
            </div>
        `;
        tr.appendChild(rowNumberTd);
        
        // خلايا البيانات
        receiptData.columns.forEach((column, colIndex) => {
            const td = document.createElement('td');
            td.className = 'editable-cell';
            td.contentEditable = true;
            td.textContent = row[column.id] || '';
            td.addEventListener('blur', function() {
                updateCellData(rowIndex, column.id, this.textContent);
            });
            tr.appendChild(td);
        });
        
        tbody.appendChild(tr);
    });
}

// فتح نافذة البحث عن الأفراد
function openPersonnelSearch() {
    const modal = new bootstrap.Modal(document.getElementById('personnelSearchModal'));
    modal.show();
}

// حفظ تلقائي للبيانات مع تحسين الأداء
function autoSave() {
    // استخدام scheduleAutoSave بدلاً من الحفظ المباشر لتقليل الطلبات
    scheduleAutoSave();
}

// دالة الحفظ التلقائي الفعلية (محسنة)
function performAutoSave() {
    try {
        // جمع البيانات من خانات الجداول أولاً
        collectPatrolDataFromInputs();
        collectShiftsDataFromInputs();

        // جمع البيانات من النموذج
        if (document.getElementById('hijriDate')) {
            receiptData.hijriDate = document.getElementById('hijriDate').value;
        }
        if (document.getElementById('gregorianDate')) {
            receiptData.gregorianDate = document.getElementById('gregorianDate').value;
        }
        if (document.getElementById('receiptNumber')) {
            receiptData.receiptNumber = document.getElementById('receiptNumber').value;
        }

        // جمع بيانات المواقع من القوائم المنسدلة
        const locationSelects = document.querySelectorAll('.location-select:not(.patrol-location-select)');
        locationSelects.forEach((select, index) => {
            if (receiptData.rows[index]) {
                receiptData.rows[index][1] = select.value || ''; // حفظ ID الموقع في العمود الثاني
            }
        });

        // جمع بيانات مواقع الدوريات من القوائم المنسدلة
        const patrolLocationSelects = document.querySelectorAll('.patrol-location-select');
        patrolLocationSelects.forEach((select, index) => {
            if (patrolData.rows[index]) {
                patrolData.rows[index][1] = select.value || ''; // حفظ ID الموقع في العمود الثاني
            }
        });

        // جمع ملاحظات الدوريات والحراس
        if (document.getElementById('patrolNotes')) {
            receiptData.patrolNotes = document.getElementById('patrolNotes').value;
        }

        if (document.getElementById('guardNotes')) {
            receiptData.guardNotes = document.getElementById('guardNotes').value;
        }

        // جمع بيانات الحراس
        if (document.getElementById('guardName')) {
            receiptData.guardName = document.getElementById('guardName').value;
        }
        if (document.getElementById('guardSignature')) {
            receiptData.guardSignature = document.getElementById('guardSignature').value;
        }
        if (document.getElementById('supervisor')) {
            receiptData.supervisor = document.getElementById('supervisor').value;
        }

        // جمع بيانات التوقيعات
        if (document.getElementById('reviewedBy')) {
            receiptData.reviewedBy = document.getElementById('reviewedBy').textContent;
        }
        if (document.getElementById('approvedBy')) {
            receiptData.approvedBy = document.getElementById('approvedBy').textContent;
        }

        // حفظ في التخزين المحلي مع تنظيف البيانات
        localStorage.setItem('receiptData', JSON.stringify(receiptData));
        localStorage.setItem('patrolData', JSON.stringify(cleanDataBeforeSave(patrolData)));
        localStorage.setItem('shiftsData', JSON.stringify(cleanDataBeforeSave(shiftsData)));
        localStorage.setItem('receiptLastSaved', new Date().toISOString());

        // حفظ جميع البيانات في قاعدة البيانات (بما في ذلك بيانات جدول الدوريات)
        saveAllDataToServer();

        console.log('تم الحفظ التلقائي للبيانات مع المواقع:', receiptData);

        // عرض مؤشر نجاح الحفظ بشكل أقل تدخلاً
        updateSaveStatus('محفوظ', 'saved');

        // إخفاء مؤشر الحفظ بعد ثانية واحدة
        setTimeout(() => {
            updateSaveStatus('', 'hidden');
        }, 1000);

    } catch (error) {
        console.error('خطأ في الحفظ التلقائي:', error);
        updateSaveStatus('خطأ', 'error');
    }
}

// تحديث مؤشر حالة الحفظ
function updateSaveStatus(message, status) {
    let statusElement = document.getElementById('saveStatus');

    // إنشاء عنصر مؤشر الحفظ إذا لم يكن موجوداً
    if (!statusElement) {
        statusElement = document.createElement('div');
        statusElement.id = 'saveStatus';
        statusElement.style.position = 'fixed';
        statusElement.style.top = '10px';
        statusElement.style.left = '10px';
        statusElement.style.padding = '4px 8px';
        statusElement.style.borderRadius = '12px';
        statusElement.style.fontSize = '11px';
        statusElement.style.fontWeight = 'normal';
        statusElement.style.zIndex = '10000';
        statusElement.style.transition = 'all 0.3s ease';
        statusElement.style.opacity = '0.8';
        document.body.appendChild(statusElement);
    }

    statusElement.textContent = message;

    // تطبيق الألوان حسب الحالة
    switch (status) {
        case 'saved':
            statusElement.style.backgroundColor = '#28a745';
            statusElement.style.color = '#fff';
            statusElement.style.display = 'block';
            break;
        case 'error':
            statusElement.style.backgroundColor = '#dc3545';
            statusElement.style.color = '#fff';
            statusElement.style.display = 'block';
            break;
        case 'hidden':
        default:
            statusElement.style.display = 'none';
            break;
    }
}

// حفظ بيانات كشف الاستلامات
function saveReceiptData() {
    try {
        // جمع البيانات من النموذج
        receiptData.hijriDate = document.getElementById('hijriDate').value;
        receiptData.gregorianDate = document.getElementById('gregorianDate').value;
        receiptData.receiptNumber = document.getElementById('receiptNumber').value;

        // جمع بيانات المواقع من القوائم المنسدلة
        const locationSelects = document.querySelectorAll('.location-select:not(.patrol-location-select)');
        locationSelects.forEach((select, index) => {
            if (receiptData.rows[index] && select.value) {
                receiptData.rows[index][1] = select.value; // حفظ ID الموقع في العمود الثاني
            }
        });

        // جمع بيانات مواقع الدوريات من القوائم المنسدلة
        const patrolLocationSelects = document.querySelectorAll('.patrol-location-select');
        patrolLocationSelects.forEach((select, index) => {
            if (patrolData.rows[index] && select.value) {
                patrolData.rows[index][1] = select.value; // حفظ ID الموقع في العمود الثاني
            }
        });

        // جمع ملاحظات الدوريات والحراس
        if (document.getElementById('patrolNotes')) {
            receiptData.patrolNotes = document.getElementById('patrolNotes').value;
        }

        if (document.getElementById('guardNotes')) {
            receiptData.guardNotes = document.getElementById('guardNotes').value;
        }

        // حفظ في التخزين المحلي
        localStorage.setItem('receiptData', JSON.stringify(receiptData));
        localStorage.setItem('receiptLastSaved', new Date().toISOString());

        // حفظ المواقع في قاعدة البيانات أيضاً لضمان عدم فقدانها
        saveLocationsToServer();

        console.log('تم حفظ البيانات مع المواقع:', receiptData);

    } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
        console.error('خطأ في حفظ البيانات');
    }
}

// دالة لتنظيف البيانات وإزالة معرفات المواقع من الخانات النصية (محسنة)
function cleanupLocationIdsFromData() {
    console.log('🧹 تنظيف معرفات المواقع من البيانات...');

    // تجاهل التنظيف إذا لم تكن المواقع محملة بعد
    if (!locationsDatabase || locationsDatabase.length === 0) {
        console.log('⏳ تأجيل التنظيف حتى تحميل المواقع...');
        return;
    }

    // قائمة معرفات المواقع المعروفة للمقارنة
    const knownLocationIds = locationsDatabase.map(loc => loc.id.toString());
    console.log('📋 معرفات المواقع المعروفة:', knownLocationIds);

    let cleanedCount = 0;

    // تنظيف بيانات جدول الدوريات
    if (patrolData && patrolData.rows) {
        patrolData.rows.forEach((row, rowIndex) => {
            row.forEach((cell, cellIndex) => {
                // تجاهل عمود الرقم (0) وعمود الموقع (1)
                if (cellIndex > 1 && cell) {
                    const cellStr = cell.toString().trim();
                    // إزالة فقط إذا كان رقم ومعرف موقع معروف وليس نص مفيد
                    if (/^\d+$/.test(cellStr) && knownLocationIds.includes(cellStr) && cellStr.length < 5) {
                        console.log(`🧹 إزالة معرف موقع من جدول الدوريات [${rowIndex}, ${cellIndex}]: ${cell}`);
                        patrolData.rows[rowIndex][cellIndex] = '';
                        cleanedCount++;
                    }
                }
            });
        });
    }

    // تنظيف بيانات جدول المناوبين
    if (shiftsData && shiftsData.rows) {
        shiftsData.rows.forEach((row, rowIndex) => {
            row.forEach((cell, cellIndex) => {
                // تجاهل عمود الرقم (0) وعمود الموقع (1)
                if (cellIndex > 1 && cell) {
                    const cellStr = cell.toString().trim();
                    // إزالة فقط إذا كان رقم ومعرف موقع معروف وليس نص مفيد
                    if (/^\d+$/.test(cellStr) && knownLocationIds.includes(cellStr) && cellStr.length < 5) {
                        console.log(`🧹 إزالة معرف موقع من جدول المناوبين [${rowIndex}, ${cellIndex}]: ${cell}`);
                        shiftsData.rows[rowIndex][cellIndex] = '';
                        cleanedCount++;
                    }
                }
            });
        });
    }

    console.log(`✅ تم تنظيف ${cleanedCount} خانة من معرفات المواقع`);
}

// إعداد مستمعي الأحداث للحفظ الفوري
function setupInputEventListeners() {
    console.log('🔧 إعداد مستمعي الأحداث للحفظ الفوري...');

    // مستمع أحداث لخانات جدول الدوريات
    document.addEventListener('input', function(event) {
        if (event.target.classList.contains('patrol-editable-cell')) {
            console.log('📝 تم تعديل خانة في جدول الدوريات');
            setTimeout(() => {
                collectPatrolDataFromInputs();
                localStorage.setItem('patrolData', JSON.stringify(patrolData));
                savePatrolDataToServer();
            }, 100);
        }
    });

    // مستمع أحداث لخانات جدول المناوبين
    document.addEventListener('input', function(event) {
        if (event.target.classList.contains('shifts-editable-cell')) {
            console.log('📝 تم تعديل خانة في جدول المناوبين');
            setTimeout(() => {
                collectShiftsDataFromInputs();
                localStorage.setItem('shiftsData', JSON.stringify(shiftsData));
                saveShiftsDataToServer();
            }, 100);
        }
    });

    // مستمع أحداث لحفظ البيانات عند فقدان التركيز
    document.addEventListener('blur', function(event) {
        if (event.target.classList.contains('patrol-editable-cell') ||
            event.target.classList.contains('shifts-editable-cell')) {
            console.log('💾 حفظ البيانات عند فقدان التركيز');
            collectPatrolDataFromInputs();
            collectShiftsDataFromInputs();
            localStorage.setItem('patrolData', JSON.stringify(patrolData));
            localStorage.setItem('shiftsData', JSON.stringify(shiftsData));
        }
    }, true);

    console.log('✅ تم إعداد مستمعي الأحداث للحفظ الفوري');
}

// دالة لاستعادة البيانات بشكل مستقر
function restoreDataStably() {
    console.log('🔄 استعادة البيانات بشكل مستقر...');

    // تأخير قصير للتأكد من تحميل الجداول
    setTimeout(() => {
        // جمع البيانات من الخانات الموجودة
        collectPatrolDataFromInputs();
        collectShiftsDataFromInputs();

        // إعادة تطبيق البيانات على الخانات
        applyDataToInputs();

        console.log('✅ تم استعادة البيانات بشكل مستقر');
    }, 200);
}

// دالة لتطبيق البيانات على خانات الإدخال
function applyDataToInputs() {
    console.log('📝 تطبيق البيانات على خانات الإدخال...');

    // تطبيق بيانات جدول الدوريات
    if (patrolData && patrolData.rows) {
        const patrolInputs = document.querySelectorAll('.patrol-editable-cell');
        patrolInputs.forEach((input, index) => {
            const rowIndex = Math.floor(index / (patrolData.headers.length - 2)); // -2 لتجاهل عمود الرقم والموقع
            const colIndex = (index % (patrolData.headers.length - 2)) + 2; // +2 لتجاهل عمود الرقم والموقع

            if (patrolData.rows[rowIndex] && patrolData.rows[rowIndex][colIndex]) {
                const value = patrolData.rows[rowIndex][colIndex];
                // تطبيق النصوص فقط (ليس الأرقام البحتة)
                if (value) {
                    const valueStr = value.toString().trim();
                    // إظهار فقط النصوص (ليس أرقام بحتة)
                    if (!/^\d+$/.test(valueStr)) {
                        input.value = value;
                    }
                }
            }
        });
    }

    // تطبيق بيانات جدول المناوبين
    if (shiftsData && shiftsData.rows) {
        const shiftsInputs = document.querySelectorAll('.shifts-editable-cell');
        shiftsInputs.forEach((input, index) => {
            const rowIndex = Math.floor(index / (shiftsData.headers.length - 2)); // -2 لتجاهل عمود الرقم والموقع
            const colIndex = (index % (shiftsData.headers.length - 2)) + 2; // +2 لتجاهل عمود الرقم والموقع

            if (shiftsData.rows[rowIndex] && shiftsData.rows[rowIndex][colIndex]) {
                const value = shiftsData.rows[rowIndex][colIndex];
                // تطبيق النصوص فقط (ليس الأرقام البحتة)
                if (value) {
                    const valueStr = value.toString().trim();
                    // إظهار فقط النصوص (ليس أرقام بحتة)
                    if (!/^\d+$/.test(valueStr)) {
                        input.value = value;
                    }
                }
            }
        });
    }
}

// دالة للتحقق من كون القيمة رقماً بحتاً (يجب إخفاؤها)
function isLocationId(value) {
    if (!value) return false;
    const valueStr = value.toString().trim();
    // إرجاع true لجميع الأرقام البحتة
    return /^\d+$/.test(valueStr);
}

// تحميل البيانات المحفوظة
function loadReceiptData() {
    console.log('📥 بدء تحميل البيانات المحفوظة...');
    try {
        const savedData = localStorage.getItem('receiptData');
        if (savedData) {
            console.log('✅ تم العثور على بيانات في localStorage');
            const parsedData = JSON.parse(savedData);

            // تحديث البيانات
            receiptData = { ...receiptData, ...parsedData };

            // تحديث النموذج - فرض التاريخ الهجري التلقائي
            if (document.getElementById('hijriDate')) {
                const currentHijri = getCurrentHijriDate();
                document.getElementById('hijriDate').value = currentHijri;
                console.log('✅ تم فرض التاريخ الهجري التلقائي:', currentHijri);
            }
            if (receiptData.gregorianDate && document.getElementById('gregorianDate')) {
                document.getElementById('gregorianDate').value = receiptData.gregorianDate;
            }
            if (receiptData.receiptNumber && document.getElementById('receiptNumber')) {
                document.getElementById('receiptNumber').value = receiptData.receiptNumber;
            }

            // تحديث ملاحظات الدوريات والحراس
            if (receiptData.patrolNotes && document.getElementById('patrolNotes')) {
                document.getElementById('patrolNotes').value = receiptData.patrolNotes;
            }
            if (receiptData.guardNotes && document.getElementById('guardNotes')) {
                document.getElementById('guardNotes').value = receiptData.guardNotes;
            }

            // تحديث بيانات الحراس
            if (receiptData.guardName && document.getElementById('guardName')) {
                document.getElementById('guardName').value = receiptData.guardName;
            }
            if (receiptData.guardSignature && document.getElementById('guardSignature')) {
                document.getElementById('guardSignature').value = receiptData.guardSignature;
            }
            if (receiptData.supervisor && document.getElementById('supervisor')) {
                document.getElementById('supervisor').value = receiptData.supervisor;
            }

            // تحديث بيانات التوقيعات
            if (receiptData.reviewedBy && document.getElementById('reviewedBy')) {
                document.getElementById('reviewedBy').textContent = receiptData.reviewedBy;
            }
            if (receiptData.approvedBy && document.getElementById('approvedBy')) {
                document.getElementById('approvedBy').textContent = receiptData.approvedBy;
            }

            // إعادة إنشاء الجداول
            generateTable();

            // تحميل بيانات جدول ملاحظات الدوريات
            const savedPatrolData = localStorage.getItem('patrolData');
            if (savedPatrolData) {
                const loadedPatrolData = JSON.parse(savedPatrolData);

                // فرض استخدام العناوين الافتراضية الجديدة دائماً
                patrolData.headers = [...DEFAULT_PATROL_HEADERS];

                // نسخ البيانات الموجودة مع التأكد من التطابق مع العناوين الجديدة
                if (loadedPatrolData.rows && loadedPatrolData.rows.length > 0) {
                    patrolData.rows = [];
                    for (let i = 0; i < loadedPatrolData.rows.length; i++) {
                        const newRow = Array(patrolData.headers.length).fill('');
                        // نسخ البيانات الموجودة (بحد أقصى طول العناوين الجديدة)
                        for (let j = 0; j < Math.min(loadedPatrolData.rows[i].length, patrolData.headers.length); j++) {
                            newRow[j] = loadedPatrolData.rows[i][j] || '';
                        }
                        patrolData.rows.push(newRow);
                    }
                }

                generatePatrolTable();

                // استعادة مواقع الدوريات
                setTimeout(() => {
                    const patrolLocationSelects = document.querySelectorAll('.patrol-location-select');
                    patrolLocationSelects.forEach((select, index) => {
                        if (patrolData.rows[index] && patrolData.rows[index][1]) {
                            select.value = patrolData.rows[index][1];
                            console.log(`📍 تم استعادة موقع الدورية ${patrolData.rows[index][1]} للصف ${index}`);
                        }
                    });

                    // استعادة البيانات النصية بشكل مستقر
                    restoreDataStably();
                }, 200);
            }

            // تحميل بيانات جدول المناوبين
            const savedShiftsData = localStorage.getItem('shiftsData');
            if (savedShiftsData) {
                const loadedShiftsData = JSON.parse(savedShiftsData);

                // فرض استخدام العناوين الافتراضية الجديدة دائماً
                shiftsData.headers = [...DEFAULT_SHIFTS_HEADERS];

                // نسخ البيانات الموجودة مع التأكد من التطابق مع العناوين الجديدة
                if (loadedShiftsData.rows && loadedShiftsData.rows.length > 0) {
                    shiftsData.rows = [];
                    for (let i = 0; i < loadedShiftsData.rows.length; i++) {
                        const newRow = Array(shiftsData.headers.length).fill('');
                        // نسخ البيانات الموجودة (بحد أقصى طول العناوين الجديدة)
                        for (let j = 0; j < Math.min(loadedShiftsData.rows[i].length, shiftsData.headers.length); j++) {
                            newRow[j] = loadedShiftsData.rows[i][j] || '';
                        }
                        shiftsData.rows.push(newRow);
                    }
                }

                generateShiftsTable();

                // استعادة مواقع المناوبين
                setTimeout(() => {
                    const shiftsLocationSelects = document.querySelectorAll('.shifts-location-select');
                    shiftsLocationSelects.forEach((select, index) => {
                        if (shiftsData.rows[index] && shiftsData.rows[index][1]) {
                            select.value = shiftsData.rows[index][1];
                            console.log(`📍 تم استعادة موقع المناوبة ${shiftsData.rows[index][1]} للصف ${index}`);
                        }
                    });

                    // استعادة البيانات النصية بشكل مستقر
                    restoreDataStably();
                }, 300);
            }

            // استعادة المواقع المحددة بعد إنشاء الجدول
            setTimeout(() => {
                const locationSelects = document.querySelectorAll('.location-select');
                locationSelects.forEach((select, index) => {
                    if (receiptData.rows[index] && receiptData.rows[index][1]) {
                        select.value = receiptData.rows[index][1];
                    }
                });
            }, 100);

            // استعادة نهائية للبيانات
            setTimeout(() => {
                restoreDataStably();

                // حفظ إضافي للتأكد من استمرارية البيانات
                setTimeout(() => {
                    collectPatrolDataFromInputs();
                    collectShiftsDataFromInputs();
                    localStorage.setItem('patrolData', JSON.stringify(patrolData));
                    localStorage.setItem('shiftsData', JSON.stringify(shiftsData));
                }, 500);
            }, 1000);

            console.log('✅ تم تحميل البيانات المحفوظة من localStorage');
        } else {
            console.log('❌ لا توجد بيانات في localStorage، محاولة التحميل من الخادم...');
            loadAllDataFromServer();
            loadPatrolDataFromServer();
            loadShiftsDataFromServer();
        }

        // تحميل إضافي من قاعدة البيانات للتأكد
        setTimeout(() => {
            console.log('🔄 تحميل إضافي من قاعدة البيانات...');
            loadAllDataFromServer();
            loadPatrolDataFromServer();
            loadShiftsDataFromServer();
        }, 2000);
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
        console.log('🔄 محاولة التحميل من الخادم كبديل...');
        loadAllDataFromServer();
    }
}

// حفظ كشف الاستلامات يدوياً (عند الضغط على زر الحفظ)
function saveReceipt() {
    try {
        // حفظ البيانات محلياً
        saveReceiptData();

        // حفظ جميع البيانات في قاعدة البيانات مع إشعار
        saveAllDataToServerWithNotification()
            .then(() => {
                // عرض إشعار النجاح فقط عند الضغط على زر الحفظ
                showAlert('💾 تم حفظ كشف الاستلامات بنجاح في قاعدة البيانات', 'success', 3000);
                console.log('✅ تم حفظ كشف الاستلامات يدوياً');
            })
            .catch(error => {
                console.error('❌ خطأ في حفظ كشف الاستلامات:', error);
                showAlert('❌ حدث خطأ أثناء حفظ كشف الاستلامات', 'error', 3000);
            });

    } catch (error) {
        console.error('❌ خطأ في حفظ كشف الاستلامات:', error);
        showAlert('❌ حدث خطأ أثناء حفظ كشف الاستلامات', 'error', 3000);
    }
}

// تصدير كشف الاستلامات إلى Excel
function exportToExcel() {
    try {
        console.log('🚀 بدء عملية التصدير...');

        // التحقق من وجود البيانات
        if (!receiptData || !receiptData.headers || !receiptData.headers[0]) {
            console.error('❌ بيانات كشف الاستلامات غير موجودة');
            console.log('لا توجد بيانات لتصديرها');
            return;
        }

        // جمع البيانات من النموذج
        const hijriDateElement = document.getElementById('hijriDate');
        const gregorianDateElement = document.getElementById('gregorianDate');
        const dayNameElement = document.getElementById('dayName');
        const receiptNumberElement = document.getElementById('receiptNumber');

        const hijriDate = hijriDateElement ? hijriDateElement.value || '' : '';
        const gregorianDate = gregorianDateElement ? gregorianDateElement.value || '' : '';
        const dayName = dayNameElement ? dayNameElement.value || '' : '';
        const receiptNumber = receiptNumberElement ? receiptNumberElement.value || '' : '';

        console.log('📋 معلومات الكشف:', { hijriDate, gregorianDate, dayName, receiptNumber });

        // جمع بيانات الجدول الرئيسي من DOM مباشرة
        const mainTableData = [];

        // جمع العناوين من DOM
        const headerElements = document.querySelectorAll('#tableHeader th');
        const mainHeaders = [];
        headerElements.forEach(th => {
            const input = th.querySelector('.editable-header');
            if (input) {
                mainHeaders.push(input.value || th.textContent.trim());
            } else {
                mainHeaders.push(th.textContent.trim());
            }
        });

        console.log('📊 عناوين الجدول الرئيسي من DOM:', mainHeaders);

        // إضافة العناوين
        if (mainHeaders.length > 0) {
            mainTableData.push([...mainHeaders]);
        } else {
            mainTableData.push([...DEFAULT_HEADERS]);
        }

        // جمع البيانات من DOM
        const tableRows = document.querySelectorAll('#receiptTableBody tr');
        console.log(`📊 عدد الصفوف في DOM: ${tableRows.length}`);

        tableRows.forEach((tr, index) => {
            const rowData = [];
            const cells = tr.querySelectorAll('td');

            cells.forEach((td, cellIndex) => {
                if (cellIndex === 0) {
                    // رقم الصف
                    rowData.push(index + 1);
                } else if (cellIndex === 1) {
                    // الموقع - من القائمة المنسدلة
                    const select = td.querySelector('.location-select');
                    if (select && select.value) {
                        const location = locationsDatabase.find(loc => loc.id == select.value);
                        rowData.push(location ? location.name : select.value);
                    } else {
                        rowData.push('');
                    }
                } else {
                    // البيانات العادية من input
                    const input = td.querySelector('.editable-cell');
                    if (input) {
                        rowData.push(input.value || '');
                    } else {
                        rowData.push(td.textContent.trim() || '');
                    }
                }
            });

            // التأكد من أن الصف يحتوي على نفس عدد الأعمدة
            while (rowData.length < mainHeaders.length) {
                rowData.push('');
            }

            mainTableData.push(rowData);
        });

        console.log('📊 بيانات الجدول الرئيسي:', mainTableData);

        // جمع بيانات جدول ملاحظات الدوريات من DOM مباشرة
        const patrolTableData = [];

        // جمع العناوين من DOM
        const patrolHeaderElements = document.querySelectorAll('#patrolTableHeader th');
        const patrolHeaders = [];
        patrolHeaderElements.forEach(th => {
            const input = th.querySelector('.editable-header');
            if (input) {
                patrolHeaders.push(input.value || th.textContent.trim());
            } else {
                patrolHeaders.push(th.textContent.trim());
            }
        });

        console.log('📊 عناوين جدول الدوريات من DOM:', patrolHeaders);

        // إضافة العناوين
        if (patrolHeaders.length > 0) {
            patrolTableData.push([...patrolHeaders]);
        } else {
            patrolTableData.push([...DEFAULT_PATROL_HEADERS]);
        }

        // جمع البيانات من DOM
        const patrolTableRows = document.querySelectorAll('#patrolTableBody tr');
        console.log(`📊 عدد صفوف الدوريات في DOM: ${patrolTableRows.length}`);

        patrolTableRows.forEach((tr, index) => {
            const rowData = [];
            const cells = tr.querySelectorAll('td');

            cells.forEach((td, cellIndex) => {
                if (cellIndex === 0) {
                    // رقم الصف
                    rowData.push(index + 1);
                } else if (cellIndex === 1) {
                    // الموقع - من القائمة المنسدلة
                    const select = td.querySelector('.patrol-location-select');
                    if (select && select.value) {
                        const location = locationsDatabase.find(loc => loc.id == select.value);
                        rowData.push(location ? location.name : select.value);
                    } else {
                        rowData.push('');
                    }
                } else {
                    // البيانات العادية من input
                    const input = td.querySelector('.editable-cell');
                    if (input) {
                        rowData.push(input.value || '');
                    } else {
                        rowData.push(td.textContent.trim() || '');
                    }
                }
            });

            // التأكد من أن الصف يحتوي على نفس عدد الأعمدة
            while (rowData.length < patrolHeaders.length) {
                rowData.push('');
            }

            patrolTableData.push(rowData);
        });

        console.log('📊 بيانات جدول الدوريات:', patrolTableData);

        // جمع بيانات جدول المناوبين من DOM مباشرة
        const shiftsTableData = [];

        // جمع العناوين من DOM
        const shiftsHeaderElements = document.querySelectorAll('#shiftsTableHeader th');
        const shiftsHeaders = [];
        shiftsHeaderElements.forEach(th => {
            const input = th.querySelector('.editable-header');
            if (input) {
                shiftsHeaders.push(input.value || th.textContent.trim());
            } else {
                shiftsHeaders.push(th.textContent.trim());
            }
        });

        console.log('📊 عناوين جدول المناوبين من DOM:', shiftsHeaders);

        // إضافة العناوين
        if (shiftsHeaders.length > 0) {
            shiftsTableData.push([...shiftsHeaders]);
        } else {
            shiftsTableData.push([...DEFAULT_SHIFTS_HEADERS]);
        }

        // جمع البيانات من DOM
        const shiftsRowElements = document.querySelectorAll('#shiftsTableBody tr');
        shiftsRowElements.forEach((tr, rowIndex) => {
            const rowData = [];
            const cells = tr.querySelectorAll('td');

            cells.forEach((td, colIndex) => {
                if (colIndex === 0) {
                    // عمود الأرقام
                    rowData.push((rowIndex + 1).toString());
                } else if (colIndex === 1) {
                    // عمود الموقع - من select
                    const select = td.querySelector('.shifts-location-select');
                    if (select && select.selectedOptions.length > 0) {
                        rowData.push(select.selectedOptions[0].text || '');
                    } else {
                        rowData.push('');
                    }
                } else {
                    // البيانات العادية من input
                    const input = td.querySelector('.editable-cell');
                    if (input) {
                        rowData.push(input.value || '');
                    } else {
                        rowData.push(td.textContent.trim() || '');
                    }
                }
            });

            // التأكد من أن الصف يحتوي على نفس عدد الأعمدة
            while (rowData.length < shiftsHeaders.length) {
                rowData.push('');
            }

            shiftsTableData.push(rowData);
        });

        console.log('📊 بيانات جدول المناوبين:', shiftsTableData);

        // التحقق من وجود بيانات للتصدير
        const hasMainData = mainTableData.length > 1; // أكثر من العناوين فقط
        const hasPatrolData = patrolTableData.length > 1; // أكثر من العناوين فقط
        const hasShiftsData = shiftsTableData.length > 1; // أكثر من العناوين فقط

        if (!hasMainData && !hasPatrolData && !hasShiftsData) {
            console.warn('⚠️ لا توجد بيانات للتصدير');
            console.log('لا توجد بيانات للتصدير');
            return;
        }

        console.log(`📊 ملخص البيانات: كشف الاستلامات (${hasMainData ? 'يحتوي على بيانات' : 'فارغ'}), كشف استلامات الدوريات (${hasPatrolData ? 'يحتوي على بيانات' : 'فارغ'}), جدول المناوبين (${hasShiftsData ? 'يحتوي على بيانات' : 'فارغ'})`);

        // إعداد البيانات للإرسال
        const exportData = {
            receipt_info: {
                hijri_date: hijriDate,
                gregorian_date: gregorianDate,
                day_name: dayName,
                receipt_number: receiptNumber
            },
            main_table: mainTableData,
            patrol_table: patrolTableData,
            shifts_table: shiftsTableData
        };

        console.log('📦 البيانات المعدة للإرسال:', exportData);

        // التحقق من وجود CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'Content-Type': 'application/json'
        };

        if (csrfToken) {
            headers['X-CSRFToken'] = csrfToken.getAttribute('content');
        }

        console.log('🌐 إرسال البيانات للخادم...');

        // إرسال البيانات للخادم لتصدير Excel
        fetch('/receipts/export-excel', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(exportData)
        })
        .then(response => {
            console.log('📡 استجابة الخادم:', response.status, response.statusText);
            if (response.ok) {
                return response.blob();
            }
            throw new Error(`فشل في تصدير الملف: ${response.status} ${response.statusText}`);
        })
        .then(blob => {
            console.log('📁 تم استلام الملف، حجم:', blob.size, 'بايت');

            // تحميل الملف
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            // اسم الملف الموحد (يحتوي دائماً على كلا الجدولين)
            const fileName = `كشف_الاستلامات_وكشف_استلامات_الدوريات_${gregorianDate || new Date().toISOString().split('T')[0]}`;

            // تحديد امتداد الملف
            const fileExtension = blob.type.includes('excel') || blob.type.includes('spreadsheet') ? '.xlsx' : '.csv';
            a.download = fileName + fileExtension;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            console.log('✅ تم تحميل الملف بنجاح');

            // رسالة نجاح موحدة
            const successMessage = '📊 تم تصدير كشف الاستلامات وكشف استلامات الدوريات وجدول المناوبين بنجاح في ملف Excel واحد';
            showAlert(successMessage, 'success', 6000);
        })
        .catch(error => {
            console.error('❌ خطأ في تصدير Excel:', error);
            console.error('حدث خطأ أثناء تصدير الملف:', error.message);
        });

    } catch (error) {
        console.error('❌ خطأ في إعداد بيانات التصدير:', error);
        console.error('حدث خطأ في إعداد البيانات للتصدير:', error.message);
    }
}



// فتح نافذة إضافة فرد
function openAddPersonnelModal() {
    const modal = new bootstrap.Modal(document.getElementById('addPersonnelModal'));
    modal.show();
}

// فتح نافذة إضافة موقع
function openAddLocationModal() {
    const modal = new bootstrap.Modal(document.getElementById('addLocationModal'));
    modal.show();
}

// إدراج الفرد في الجدول
function insertPersonnel() {
    const insertBtn = document.getElementById('insertPersonnelBtn');
    const personnelData = insertBtn.getAttribute('data-personnel');

    if (personnelData) {
        const personnel = JSON.parse(personnelData);

        // البحث عن أول خلية فارغة في الجدول لإدراج اسم الفرد
        let inserted = false;
        for (let rowIndex = 0; rowIndex < receiptData.rows.length && !inserted; rowIndex++) {
            for (let colIndex = 2; colIndex < receiptData.rows[rowIndex].length && !inserted; colIndex++) {
                if (!receiptData.rows[rowIndex][colIndex] || receiptData.rows[rowIndex][colIndex].trim() === '') {
                    receiptData.rows[rowIndex][colIndex] = personnel.name;
                    inserted = true;
                    break;
                }
            }
        }

        if (inserted) {
            generateTable();
            autoSave();

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('personnelSearchModal'));
            modal.hide();

            // إعادة تعيين النموذج
            document.getElementById('searchNationalId').value = '';
            document.getElementById('searchResults').innerHTML = '';
            document.getElementById('personnelDetails').style.display = 'none';
            document.getElementById('insertPersonnelBtn').disabled = true;
        } else {
            alert('الجدول ممتلئ، يرجى إضافة صف جديد أو إفراغ خلية موجودة');
        }
    }
}

// إضافة صف جديد (للتوافق مع الزر في HTML)
function addNewRow() {
    addRow();
}

// متغيرات لتتبع الطلبات المعلقة ومنع التكرار
let pendingSearchRequests = new Set();
let searchCache = new Map();
let lastSearchTime = 0;
const SEARCH_DEBOUNCE_TIME = 500; // نصف ثانية

// البحث التلقائي عن الأفراد في الخلايا (محسن)
async function autoSearchPersonnel(inputElement, value) {
    // التحقق من أن القيمة رقم هوية (10 أرقام)
    if (!value || value.length !== 10 || !/^\d+$/.test(value)) {
        // إزالة التنسيق للقيم غير الصحيحة
        inputElement.title = '';
        inputElement.style.backgroundColor = '';
        return;
    }

    // منع الطلبات المتكررة السريعة
    const currentTime = Date.now();
    if (currentTime - lastSearchTime < SEARCH_DEBOUNCE_TIME) {
        console.log(`⏳ تأخير البحث لـ ${value}`);
        return;
    }
    lastSearchTime = currentTime;

    // التحقق من وجود طلب معلق لنفس القيمة
    if (pendingSearchRequests.has(value)) {
        console.log(`🔄 طلب بحث معلق بالفعل لـ: ${value}`);
        return;
    }

    // التحقق من الذاكرة المؤقتة أولاً
    if (searchCache.has(value)) {
        const cachedPersonnel = searchCache.get(value);
        applyPersonnelToInput(inputElement, cachedPersonnel);
        return;
    }

    // البحث في قاعدة البيانات المحلية أولاً
    let personnel = personnelDatabase.find(p => p.nationalId === value);

    // إذا لم يتم العثور على الفرد، جرب البحث في الخادم
    if (!personnel) {
        try {
            // إضافة الطلب إلى قائمة الطلبات المعلقة
            pendingSearchRequests.add(value);
            console.log(`🔍 البحث عن الفرد: ${value}`);

            const response = await fetch(`/receipts/personnel/search?national_id=${value}`);

            if (response.ok) {
                const data = await response.json();
                if (data && data.success && data.personnel) {
                    personnel = {
                        nationalId: data.personnel.national_id,
                        name: data.personnel.name,
                        rank: data.personnel.rank || 'غير محدد',
                        unit: data.personnel.unit || 'غير محدد'
                    };

                    // إضافة الفرد إلى قاعدة البيانات المحلية والذاكرة المؤقتة
                    personnelDatabase.push(personnel);
                    searchCache.set(value, personnel);

                    console.log(`✅ تم العثور على الفرد: ${personnel.name}`);
                } else {
                    console.log(`❌ لم يتم العثور على فرد برقم: ${value}`);
                    // حفظ النتيجة السلبية في الذاكرة المؤقتة لمنع البحث المتكرر
                    searchCache.set(value, null);
                }
            } else {
                console.error(`❌ خطأ في الاستجابة: ${response.status}`);
            }
        } catch (error) {
            console.error('❌ خطأ في البحث عن الفرد:', error);
        } finally {
            // إزالة الطلب من قائمة الطلبات المعلقة
            pendingSearchRequests.delete(value);
        }
    }

    // تطبيق النتيجة على الحقل
    applyPersonnelToInput(inputElement, personnel);
}

// تطبيق بيانات الفرد على الحقل
function applyPersonnelToInput(inputElement, personnel) {
    if (personnel) {
        // تحديث قيمة الحقل
        inputElement.value = personnel.name;

        // إضافة tooltip
        inputElement.title = `${personnel.name} - ${personnel.rank} - ${personnel.unit}`;

        // تغيير لون الخلفية للإشارة إلى وجود بيانات
        inputElement.style.backgroundColor = '#e8f5e8';

        // إطلاق حدث التحديث
        const event = new Event('blur');
        inputElement.dispatchEvent(event);

        // عرض رسالة نجاح صغيرة
        showPersonnelFound(personnel.name);
    } else {
        // إزالة التنسيق إذا لم يتم العثور على الفرد
        inputElement.title = '';
        inputElement.style.backgroundColor = '';
    }
}

// عرض رسالة وجود الفرد (محسنة)
function showPersonnelFound(name) {
    // إزالة الرسائل السابقة لمنع التراكم
    const existingMessages = document.querySelectorAll('.personnel-found-message');
    existingMessages.forEach(msg => {
        if (msg.parentNode) {
            msg.parentNode.removeChild(msg);
        }
    });

    // إنشاء عنصر الرسالة
    const message = document.createElement('div');
    message.className = 'personnel-found-message';
    message.style.position = 'fixed';
    message.style.top = '50px';
    message.style.left = '10px';
    message.style.backgroundColor = '#28a745';
    message.style.color = '#fff';
    message.style.padding = '8px 12px';
    message.style.borderRadius = '4px';
    message.style.fontSize = '12px';
    message.style.zIndex = '10001';
    message.style.opacity = '0.9';
    message.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
    message.textContent = `✅ تم العثور على: ${name}`;

    document.body.appendChild(message);

    // إزالة الرسالة بعد ثانيتين
    setTimeout(() => {
        if (message.parentNode) {
            message.parentNode.removeChild(message);
        }
    }, 2000);
}

// تنظيف الذاكرة المؤقتة للبحث (كل 5 دقائق)
setInterval(() => {
    if (searchCache.size > 100) {
        console.log('🧹 تنظيف الذاكرة المؤقتة للبحث');
        searchCache.clear();
    }
}, 5 * 60 * 1000);

// دالة debounced للبحث لتقليل الطلبات
let searchTimeouts = new Map();

function debouncedAutoSearchPersonnel(inputElement, value) {
    const elementId = inputElement.id || Math.random().toString(36);

    // إلغاء المهلة السابقة إن وجدت
    if (searchTimeouts.has(elementId)) {
        clearTimeout(searchTimeouts.get(elementId));
    }

    // تعيين مهلة جديدة
    const timeoutId = setTimeout(() => {
        autoSearchPersonnel(inputElement, value);
        searchTimeouts.delete(elementId);
    }, SEARCH_DEBOUNCE_TIME);

    searchTimeouts.set(elementId, timeoutId);
}

// تحسين أداء البحث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل قاعدة بيانات الأفراد مسبقاً إذا كانت فارغة
    if (personnelDatabase.length === 0) {
        console.log('📋 تحميل قاعدة بيانات الأفراد...');
        // يمكن إضافة تحميل مسبق هنا إذا لزم الأمر
    }

    // عرض رسالة تأكيد تحسين الأداء
    console.log('✅ تم تطبيق تحسينات الأداء:');
    console.log('   🔄 Debouncing للبحث التلقائي');
    console.log('   💾 Debouncing لوظائف الحفظ');
    console.log('   🚫 منع الطلبات المتكررة');
    console.log('   🧹 تنظيف الذاكرة المؤقتة');
});

// ===============================
// وظائف جدول كشف استلامات الدوريات
// ===============================

// إنشاء جدول كشف استلامات الدوريات
function generatePatrolTable() {
    generatePatrolTableHeader();
    generatePatrolTableBody();

    // تحميل المواقع المحفوظة بعد إنشاء الجدول
    setTimeout(() => {
        loadPatrolLocationsFromServer();
        // إعداد مستمعي الأحداث بعد إنشاء الجدول
        setupPatrolTableEventListeners();
        setupInputEventListeners(); // إعادة إعداد مستمعي الحفظ الفوري
    }, 100);

    console.log('✅ تم إنشاء جدول كشف استلامات الدوريات مع مستمعي الأحداث');
}

// إنشاء رأس جدول كشف استلامات الدوريات
function generatePatrolTableHeader() {
    const thead = document.getElementById('patrolTableHeader');
    if (!thead) {
        console.error('❌ عنصر patrolTableHeader غير موجود');
        return;
    }

    if (!patrolData.headers || patrolData.headers.length === 0) {
        console.error('❌ عناوين جدول الدوريات غير محددة');
        patrolData.headers = [...DEFAULT_PATROL_HEADERS];
    }

    thead.innerHTML = '';
    const headerRow = document.createElement('tr');

    patrolData.headers.forEach((headerText, index) => {
        const th = document.createElement('th');
        th.className = 'text-center align-middle';

        if (index === 0) {
            // عمود الرقم - غير قابل للتحرير
            th.innerHTML = headerText;
            th.className += ' row-number';
        } else {
            // العناوين القابلة للتحرير
            th.innerHTML = `
                <input type="text" class="editable-header" value="${headerText}"
                       onchange="updatePatrolHeader(${index}, this.value)"
                       onblur="updatePatrolHeader(${index}, this.value)">
                <div class="column-controls">
                    <button type="button" class="control-btn btn-add" onclick="addPatrolColumnAfter(${index})" title="إضافة عمود">
                        <i class="fas fa-plus"></i>
                    </button>
                    ${patrolData.headers.length > 3 ? `
                    <button type="button" class="control-btn btn-delete" onclick="deletePatrolColumn(${index})" title="حذف العمود">
                        <i class="fas fa-times"></i>
                    </button>
                    ` : ''}
                </div>
            `;
        }

        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
}

// إنشاء محتوى جدول كشف استلامات الدوريات
function generatePatrolTableBody() {
    const tbody = document.getElementById('patrolTableBody');
    if (!tbody) {
        console.error('❌ عنصر patrolTableBody غير موجود');
        return;
    }

    tbody.innerHTML = '';

    // التأكد من وجود صفوف افتراضية
    if (!patrolData.rows || patrolData.rows.length === 0) {
        console.warn('⚠️ صفوف جدول الدوريات فارغة، إنشاء صفوف افتراضية');
        patrolData.rows = [];
        for (let i = 0; i < 6; i++) {
            patrolData.rows.push(Array(patrolData.headers.length).fill(''));
        }
    }

    patrolData.rows.forEach((rowData, rowIndex) => {
        const row = document.createElement('tr');

        rowData.forEach((cellData, cellIndex) => {
            const td = document.createElement('td');

            if (cellIndex === 0) {
                // عمود الرقم - نفس تصميم كشف الاستلامات
                td.innerHTML = `
                    <span class="row-number">${rowIndex + 1}</span>
                    <div class="row-controls">
                        <button type="button" class="control-btn btn-add" onclick="addPatrolRowAfter(${rowIndex})" title="إضافة صف">
                            <i class="fas fa-plus"></i>
                        </button>
                        ${patrolData.rows.length > 1 ? `
                        <button type="button" class="control-btn btn-delete" onclick="deletePatrolRow(${rowIndex})" title="حذف الصف">
                            <i class="fas fa-times"></i>
                        </button>
                        ` : ''}
                    </div>
                `;
                td.className = 'row-number-cell';
            } else if (cellIndex === 1) {
                // عمود الموقع - قائمة منسدلة (نفس كشف الاستلامات)
                td.innerHTML = `
                    <select class="location-select patrol-location-select" onchange="updatePatrolCell(${rowIndex}, ${cellIndex}, this.value); savePatrolLocationsToServer(); autoSave();">
                        <option value="">اختر الموقع</option>
                    </select>
                `;

                // تحميل المواقع
                const select = td.querySelector('.patrol-location-select');
                locationsDatabase.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    if (location.id == cellData) {
                        option.selected = true;
                    }
                    select.appendChild(option);
                });

                // إضافة نفس classes المستخدمة في الجدول الرئيسي
                td.className = 'text-center align-middle';
            } else {
                // الخلايا العادية القابلة للتحرير مع البحث التلقائي عن الأفراد
                // عدم إظهار أي أرقام بحتة - إظهار النصوص فقط
                let displayValue = '';
                if (cellData) {
                    const cellStr = cellData.toString().trim();
                    // إظهار فقط النصوص (ليس أرقام بحتة)
                    if (!/^\d+$/.test(cellStr)) {
                        displayValue = cellData;
                    }
                }

                td.innerHTML = `
                    <input type="text" class="editable-cell patrol-editable-cell" value="${displayValue}"
                           onchange="updatePatrolCell(${rowIndex}, ${cellIndex}, this.value); autoSave();"
                           onblur="updatePatrolCell(${rowIndex}, ${cellIndex}, this.value); autoSave();"
                           oninput="updatePatrolCell(${rowIndex}, ${cellIndex}, this.value); debouncedAutoSearchPersonnel(this, this.value); autoSave();"
                           onkeyup="updatePatrolCell(${rowIndex}, ${cellIndex}, this.value); autoSave();">
                `;

                // إضافة نفس classes المستخدمة في الجدول الرئيسي
                td.className = 'text-center align-middle';
            }

            row.appendChild(td);
        });

        tbody.appendChild(row);
    });
}

// تحديث عنوان عمود في جدول ملاحظات الدوريات
function updatePatrolHeader(columnIndex, newValue) {
    patrolData.headers[columnIndex] = newValue;
    console.log(`📝 تم تحديث عنوان العمود ${columnIndex} في جدول الدوريات إلى: "${newValue}"`);

    // حفظ محلي فقط
    localStorage.setItem('patrolData', JSON.stringify(patrolData));

    // حفظ فوري في قاعدة البيانات
    savePatrolDataToServer();
}

// تحديث خلية في جدول ملاحظات الدوريات
function updatePatrolCell(rowIndex, columnIndex, newValue) {
    if (!patrolData.rows[rowIndex]) {
        patrolData.rows[rowIndex] = Array(patrolData.headers.length).fill('');
    }

    patrolData.rows[rowIndex][columnIndex] = newValue;
    console.log(`📝 تم تحديث الخلية [${rowIndex}, ${columnIndex}] في جدول الدوريات إلى: "${newValue}"`);

    // جمع جميع البيانات من الخانات للتأكد من الحفظ الصحيح
    collectPatrolDataFromInputs();

    // حفظ محلي فوري مع تنظيف البيانات
    const cleanedData = cleanDataBeforeSave(patrolData);
    localStorage.setItem('patrolData', JSON.stringify(cleanedData));

    // حفظ فوري في قاعدة البيانات
    savePatrolDataToServer();
}

// دالة لجمع البيانات من خانات الإدخال في جدول الدوريات
function collectPatrolDataFromInputs() {
    console.log('📊 جمع البيانات من خانات جدول الدوريات...');

    // جمع البيانات من جميع الخانات النصية
    const patrolInputs = document.querySelectorAll('.patrol-editable-cell');
    patrolInputs.forEach((input, index) => {
        const rowIndex = Math.floor(index / (patrolData.headers.length - 1)); // -1 لأن العمود الأول ليس خانة إدخال
        const colIndex = (index % (patrolData.headers.length - 1)) + 2; // +2 لتجاهل عمود الرقم والموقع

        if (patrolData.rows[rowIndex] && colIndex < patrolData.headers.length) {
            const value = input.value || '';
            if (patrolData.rows[rowIndex][colIndex] !== value) {
                console.log(`📝 تحديث البيانات من الخانة [${rowIndex}, ${colIndex}]: "${value}"`);
                patrolData.rows[rowIndex][colIndex] = value;
            }
        }
    });

    // جمع بيانات المواقع من القوائم المنسدلة
    const patrolLocationSelects = document.querySelectorAll('.patrol-location-select');
    patrolLocationSelects.forEach((select, index) => {
        if (patrolData.rows[index]) {
            patrolData.rows[index][1] = select.value || '';
        }
    });
}

// إضافة عمود جديد في جدول ملاحظات الدوريات
function addPatrolColumn() {
    patrolData.headers.push('عمود جديد');

    // إضافة خلية فارغة لكل صف
    patrolData.rows.forEach(row => {
        row.push('');
    });

    generatePatrolTable();
    autoSave();
    savePatrolDataToServer();
}

// إضافة عمود بعد عمود محدد في جدول ملاحظات الدوريات
function addPatrolColumnAfter(columnIndex) {
    patrolData.headers.splice(columnIndex + 1, 0, 'عمود جديد');

    // إضافة خلية فارغة لكل صف في الموضع المحدد
    patrolData.rows.forEach(row => {
        row.splice(columnIndex + 1, 0, '');
    });

    generatePatrolTable();
    autoSave();
    savePatrolDataToServer();
}

// حذف عمود من جدول ملاحظات الدوريات
function deletePatrolColumn(columnIndex) {
    if (patrolData.headers.length <= 3) {
        alert('لا يمكن حذف المزيد من الأعمدة');
        return;
    }

    patrolData.headers.splice(columnIndex, 1);

    // حذف الخلية المقابلة من كل صف
    patrolData.rows.forEach(row => {
        row.splice(columnIndex, 1);
    });

    generatePatrolTable();
    autoSave();
    savePatrolDataToServer();
}

// إضافة صف جديد في جدول ملاحظات الدوريات
function addPatrolRow() {
    patrolData.rows.push(Array(patrolData.headers.length).fill(''));
    generatePatrolTable();
    autoSave();
    savePatrolDataToServer();
}

// إضافة صف بعد صف محدد في جدول ملاحظات الدوريات
function addPatrolRowAfter(rowIndex) {
    patrolData.rows.splice(rowIndex + 1, 0, Array(patrolData.headers.length).fill(''));
    generatePatrolTable();
    autoSave();
    savePatrolDataToServer();
}

// حذف صف من جدول ملاحظات الدوريات
function deletePatrolRow(rowIndex) {
    if (patrolData.rows.length <= 1) {
        showAlert('لا يمكن حذف الصف الوحيد المتبقي', 'warning');
        return;
    }

    patrolData.rows.splice(rowIndex, 1);
    generatePatrolTable();
    autoSave();
}

// تفريغ جدول كشف استلامات الدوريات
function resetPatrolTable() {
    if (confirm('هل أنت متأكد من تفريغ جدول كشف استلامات الدوريات؟ سيتم الاحتفاظ بالمواقع وإعادة تعيين العناوين للقيم الافتراضية.')) {
        // حفظ المواقع الحالية قبل التفريغ
        const currentPatrolLocations = [];
        const patrolLocationSelects = document.querySelectorAll('.patrol-location-select');
        patrolLocationSelects.forEach((select, index) => {
            if (select.value) {
                currentPatrolLocations[index] = select.value;
            }
        });

        // فرض استخدام العناوين الافتراضية الجديدة
        patrolData.headers = [...DEFAULT_PATROL_HEADERS];
        patrolData.rows = [];

        // إنشاء صفوف افتراضية
        for (let i = 0; i < 6; i++) {
            patrolData.rows.push(Array(patrolData.headers.length).fill(''));
        }

        // استعادة المواقع في الصفوف الجديدة
        currentPatrolLocations.forEach((locationId, index) => {
            if (patrolData.rows[index]) {
                patrolData.rows[index][1] = locationId;
            }
        });

        generatePatrolTable();

        // استعادة المواقع في القوائم المنسدلة
        setTimeout(() => {
            const newPatrolLocationSelects = document.querySelectorAll('.patrol-location-select');
            newPatrolLocationSelects.forEach((select, index) => {
                if (currentPatrolLocations[index]) {
                    select.value = currentPatrolLocations[index];
                }
            });
        }, 100);

        autoSave();
        showAlert('تم تفريغ جدول كشف استلامات الدوريات مع الاحتفاظ بالمواقع وإعادة تعيين العناوين', 'success');
    }
}

// إعادة تعيين عناوين جدول كشف استلامات الدوريات للقيم الافتراضية
function resetPatrolHeaders() {
    if (confirm('هل تريد إعادة تعيين عناوين جدول كشف استلامات الدوريات للقيم الافتراضية؟')) {
        // حفظ البيانات الحالية
        const currentData = patrolData.rows.map(row => [...row]);

        // إعادة تعيين العناوين
        patrolData.headers = [...DEFAULT_PATROL_HEADERS];

        // تحديث البيانات لتتطابق مع العناوين الجديدة
        patrolData.rows = [];
        for (let i = 0; i < currentData.length; i++) {
            const newRow = Array(patrolData.headers.length).fill('');
            // نسخ البيانات الموجودة (بحد أقصى طول العناوين الجديدة)
            for (let j = 0; j < Math.min(currentData[i].length, patrolData.headers.length); j++) {
                newRow[j] = currentData[i][j] || '';
            }
            patrolData.rows.push(newRow);
        }

        generatePatrolTable();
        autoSave();
        showAlert('تم إعادة تعيين عناوين جدول كشف استلامات الدوريات للقيم الافتراضية', 'success');
    }
}

// حفظ مواقع جدول ملاحظات الدوريات في الخادم مع debouncing
function savePatrolLocationsToServer() {
    // إلغاء أي حفظ سابق
    if (saveTimeouts.patrolLocations) {
        clearTimeout(saveTimeouts.patrolLocations);
    }

    // تعيين مهلة جديدة
    saveTimeouts.patrolLocations = setTimeout(() => {
        try {
            const currentPatrolLocations = {};
            const patrolLocationSelects = document.querySelectorAll('.patrol-location-select');
            let hasLocations = false;

            patrolLocationSelects.forEach((select, index) => {
                if (select.value && select.value !== '') {
                    currentPatrolLocations[index] = select.value;
                    hasLocations = true;
                    console.log(`💾 حفظ موقع الدورية ${select.value} للصف ${index}`);
                }
            });

            // لا نحفظ إذا لم تكن هناك مواقع محددة
            if (!hasLocations) {
                console.log('ℹ️ لا توجد مواقع دوريات لحفظها');
                return;
            }

            console.log('📤 إرسال مواقع الدوريات للخادم:', currentPatrolLocations);

            return fetch('/receipts/api/save-patrol-locations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    locations: currentPatrolLocations,
                    timestamp: new Date().toISOString()
                })
            })
            .then(response => {
                console.log('📡 استجابة حفظ مواقع الدوريات:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('✅ تم حفظ مواقع الدوريات في الخادم:', data);
                return data;
            })
            .catch(error => {
                console.error('❌ خطأ في حفظ مواقع الدوريات:', error);
                throw error;
            });
        } catch (error) {
            console.error('❌ خطأ في حفظ مواقع الدوريات:', error);
            return Promise.reject(error);
        }
        saveTimeouts.patrolLocations = null;
    }, 300); // 300ms debounce
}

// تحميل مواقع جدول ملاحظات الدوريات من الخادم
function loadPatrolLocationsFromServer() {
    console.log('🔄 بدء تحميل مواقع الدوريات من الخادم...');
    return fetch('/receipts/api/get-patrol-locations')
        .then(response => {
            console.log('📡 استجابة الخادم لمواقع الدوريات:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('📦 بيانات مواقع الدوريات المستلمة:', data);
            if (data.success && data.locations && Object.keys(data.locations).length > 0) {
                console.log('✅ تم تحميل مواقع الدوريات من الخادم:', data.locations);

                // تطبيق المواقع على الجدول فوراً
                applyPatrolLocationsToTable(data.locations);

                // تطبيق المواقع على البيانات أيضاً
                Object.keys(data.locations).forEach(rowIndex => {
                    const index = parseInt(rowIndex);
                    if (patrolData.rows[index]) {
                        patrolData.rows[index][1] = data.locations[rowIndex];
                        console.log(`💾 تم تحديث بيانات الدوريات للصف ${index}: ${data.locations[rowIndex]}`);
                    }
                });

                return data.locations;
            } else {
                console.log('ℹ️ لا توجد مواقع دوريات محفوظة أو فشل في التحميل');
                return {};
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل مواقع الدوريات:', error);
            return {};
        });
}

// تطبيق مواقع الدوريات على الجدول
function applyPatrolLocationsToTable(locations) {
    const patrolLocationSelects = document.querySelectorAll('.patrol-location-select');
    console.log('🎯 عدد القوائم المنسدلة للدوريات الموجودة:', patrolLocationSelects.length);
    console.log('📍 مواقع الدوريات المراد تطبيقها:', locations);

    patrolLocationSelects.forEach((select, index) => {
        const locationId = locations[index.toString()];
        if (locationId) {
            console.log(`📍 تطبيق موقع الدورية ${locationId} على الصف ${index}`);
            select.value = locationId;

            // التأكد من أن الخيار موجود في القائمة
            const option = select.querySelector(`option[value="${locationId}"]`);
            if (option) {
                option.selected = true;
                console.log(`✅ تم تحديد موقع الدورية "${option.textContent}" للصف ${index}`);
            } else {
                console.warn(`⚠️ موقع الدورية ${locationId} غير موجود في القائمة للصف ${index}`);
            }
        }
    });
}

// تهيئة الصفحة عند التحميل
// وظائف إضافية يمكن تطويرها لاحقاً

// 🔍 فحص الدوال المطلوبة
console.log('🔍 فحص الدوال المطلوبة...');
const requiredFunctions = [
    'addColumn', 'addPatrolColumn', 'generateTable', 'generatePatrolTable',
    'initializeReceipt', 'updateCell', 'updatePatrolCell'
];

requiredFunctions.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✅ الدالة ${funcName} موجودة`);
    } else {
        console.error(`❌ الدالة ${funcName} مفقودة`);
    }
});

console.log('✅ تم تحميل ملف receipts.js بالكامل');

// ===============================
// وظائف جدول المناوبين
// ===============================

// إنشاء جدول المناوبين
function generateShiftsTable() {
    console.log('🔨 بدء إنشاء جدول المناوبين...');
    console.log('📊 بيانات المناوبين:', shiftsData);

    // التأكد من وجود البيانات
    if (!shiftsData || !shiftsData.headers || shiftsData.headers.length === 0) {
        console.error('❌ بيانات جدول المناوبين غير صحيحة');
        shiftsData = {
            headers: [...DEFAULT_SHIFTS_HEADERS],
            rows: []
        };
        // إنشاء صف واحد افتراضي
        shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));
    }

    generateShiftsTableHeader();
    generateShiftsTableBody();

    // إعداد مستمعي الأحداث بعد إنشاء الجدول
    setTimeout(() => {
        setupShiftsTableEventListeners();
        setupInputEventListeners(); // إعادة إعداد مستمعي الحفظ الفوري
    }, 100);

    console.log('✅ تم إنشاء جدول المناوبين مع مستمعي الأحداث');
}

// إنشاء رأس جدول المناوبين
function generateShiftsTableHeader() {
    const thead = document.getElementById('shiftsTableHeader');
    if (!thead) {
        console.error('❌ لم يتم العثور على عنصر shiftsTableHeader');
        return;
    }

    thead.innerHTML = '';
    const headerRow = document.createElement('tr');

    shiftsData.headers.forEach((header, index) => {
        const th = document.createElement('th');
        th.className = 'text-center';
        th.style.position = 'relative';

        if (index === 0) {
            // عمود الأرقام
            th.style.width = '60px';
            th.style.backgroundColor = '#4a4a4a';
            th.style.color = 'white';
            th.innerHTML = `
                <span style="color: white; font-weight: bold;">${header}</span>
            `;
        } else {
            // باقي الأعمدة
            th.innerHTML = `
                <input type="text" class="editable-header"
                       value="${header}"
                       onchange="updateShiftsHeader(${index}, this.value)"
                       style="background: transparent; border: none; color: var(--text-primary); font-weight: bold; text-align: center; width: 100%;">
                <div class="column-controls">
                    <button type="button" class="control-btn btn-add" onclick="addShiftsColumnAfter(${index})" title="إضافة عمود">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button type="button" class="control-btn btn-delete" onclick="deleteShiftsColumn(${index})" title="حذف عمود">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        }

        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
}

// إنشاء محتوى جدول المناوبين
function generateShiftsTableBody() {
    const tbody = document.getElementById('shiftsTableBody');
    if (!tbody) {
        console.error('❌ لم يتم العثور على عنصر shiftsTableBody');
        return;
    }

    tbody.innerHTML = '';

    // التأكد من وجود صفوف
    if (!shiftsData.rows || shiftsData.rows.length === 0) {
        console.warn('⚠️ لا توجد صفوف في جدول المناوبين، إنشاء صف افتراضي');
        shiftsData.rows = [];
        shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));
    }

    console.log('📊 إنشاء صفوف الجدول:', shiftsData.rows.length, 'صف');

    shiftsData.rows.forEach((row, rowIndex) => {
        const tr = document.createElement('tr');

        shiftsData.headers.forEach((header, colIndex) => {
            const td = document.createElement('td');
            td.className = 'text-center';
            td.style.position = 'relative';

            if (colIndex === 0) {
                // عمود الأرقام مع أزرار التحكم
                td.innerHTML = `
                    <span class="row-number">${rowIndex + 1}</span>
                    <div class="row-controls">
                        <button type="button" class="control-btn btn-add" onclick="addShiftsRowAfter(${rowIndex})" title="إضافة صف">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button type="button" class="control-btn btn-delete" onclick="deleteShiftsRow(${rowIndex})" title="حذف صف">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                td.className = 'row-number-cell';
            } else if (colIndex === 1) {
                // عمود الموقع - قائمة منسدلة
                td.innerHTML = `
                    <select class="location-select shifts-location-select"
                            onchange="updateShiftsCell(${rowIndex}, ${colIndex}, this.value); saveShiftsLocationsToServer(); autoSave();"
                            data-row="${rowIndex}" data-col="${colIndex}">
                        <option value="">اختر الموقع</option>
                    </select>
                `;

                // تحميل المواقع في القائمة المنسدلة
                const select = td.querySelector('.shifts-location-select');
                if (locationsDatabase && locationsDatabase.length > 0) {
                    locationsDatabase.forEach(location => {
                        const option = document.createElement('option');
                        option.value = location.id;
                        option.textContent = location.name;
                        if (location.id == row[colIndex]) {
                            option.selected = true;
                        }
                        select.appendChild(option);
                    });
                } else {
                    // إذا لم تكن المواقع محملة، حاول تحميلها
                    loadLocationsFromServer().then(() => {
                        locationsDatabase.forEach(location => {
                            const option = document.createElement('option');
                            option.value = location.id;
                            option.textContent = location.name;
                            if (location.id == row[colIndex]) {
                                option.selected = true;
                            }
                            select.appendChild(option);
                        });
                    });
                }

                // إضافة نفس classes المستخدمة في الجداول الأخرى
                td.className = 'text-center align-middle';
            } else {
                // باقي الخلايا - حقول نص قابلة للتحرير مع البحث التلقائي
                // عدم إظهار أي أرقام بحتة - إظهار النصوص فقط
                const cellValue = row[colIndex] || '';
                let displayValue = '';
                if (cellValue) {
                    const cellStr = cellValue.toString().trim();
                    // إظهار فقط النصوص (ليس أرقام بحتة)
                    if (!/^\d+$/.test(cellStr)) {
                        displayValue = cellValue;
                    }
                }

                td.innerHTML = `
                    <input type="text" class="editable-cell shifts-editable-cell"
                           value="${displayValue}"
                           onchange="updateShiftsCell(${rowIndex}, ${colIndex}, this.value); autoSave();"
                           onblur="updateShiftsCell(${rowIndex}, ${colIndex}, this.value); autoSave();"
                           oninput="updateShiftsCell(${rowIndex}, ${colIndex}, this.value); debouncedAutoSearchPersonnel(this, this.value); autoSave();"
                           onkeyup="updateShiftsCell(${rowIndex}, ${colIndex}, this.value); autoSave();"
                           placeholder="">
                `;

                // إضافة نفس classes المستخدمة في الجداول الأخرى
                td.className = 'text-center align-middle';
            }

            tr.appendChild(td);
        });

        tbody.appendChild(tr);
    });
}

// تحديث عنوان عمود في جدول المناوبين
function updateShiftsHeader(columnIndex, newValue) {
    shiftsData.headers[columnIndex] = newValue;
    console.log(`📝 تم تحديث عنوان العمود ${columnIndex} في جدول المناوبين إلى: "${newValue}"`);

    // حفظ محلي فقط
    localStorage.setItem('shiftsData', JSON.stringify(shiftsData));

    // حفظ فوري في قاعدة البيانات
    saveShiftsDataToServer();
}

// تحديث خلية في جدول المناوبين
function updateShiftsCell(rowIndex, columnIndex, newValue) {
    if (!shiftsData.rows[rowIndex]) {
        shiftsData.rows[rowIndex] = Array(shiftsData.headers.length).fill('');
    }

    shiftsData.rows[rowIndex][columnIndex] = newValue;
    console.log(`📝 تم تحديث الخلية [${rowIndex}][${columnIndex}] في جدول المناوبين إلى: "${newValue}"`);

    // جمع جميع البيانات من الخانات للتأكد من الحفظ الصحيح
    collectShiftsDataFromInputs();

    // حفظ محلي فوري مع تنظيف البيانات
    const cleanedData = cleanDataBeforeSave(shiftsData);
    localStorage.setItem('shiftsData', JSON.stringify(cleanedData));

    // حفظ فوري في قاعدة البيانات
    saveShiftsDataToServer();
}

// دالة لجمع البيانات من خانات الإدخال في جدول المناوبين
function collectShiftsDataFromInputs() {
    console.log('📊 جمع البيانات من خانات جدول المناوبين...');

    // جمع البيانات من جميع الخانات النصية
    const shiftsInputs = document.querySelectorAll('.shifts-editable-cell');
    shiftsInputs.forEach((input, index) => {
        const rowIndex = Math.floor(index / (shiftsData.headers.length - 1)); // -1 لأن العمود الأول ليس خانة إدخال
        const colIndex = (index % (shiftsData.headers.length - 1)) + 2; // +2 لتجاهل عمود الرقم والموقع

        if (shiftsData.rows[rowIndex] && colIndex < shiftsData.headers.length) {
            const value = input.value || '';
            if (shiftsData.rows[rowIndex][colIndex] !== value) {
                console.log(`📝 تحديث البيانات من الخانة [${rowIndex}, ${colIndex}]: "${value}"`);
                shiftsData.rows[rowIndex][colIndex] = value;
            }
        }
    });

    // جمع بيانات المواقع من القوائم المنسدلة
    const shiftsLocationSelects = document.querySelectorAll('.shifts-location-select');
    shiftsLocationSelects.forEach((select, index) => {
        if (shiftsData.rows[index]) {
            shiftsData.rows[index][1] = select.value || '';
        }
    });
}

// دالة لتنظيف البيانات من الأرقام البحتة قبل الحفظ (الاحتفاظ بالنصوص فقط)
function cleanDataBeforeSave(data) {
    if (!data || !data.rows) return data;

    const cleanedData = JSON.parse(JSON.stringify(data)); // نسخة عميقة

    cleanedData.rows.forEach((row, rowIndex) => {
        row.forEach((cell, cellIndex) => {
            // تجاهل عمود الرقم (0) وعمود الموقع (1)
            if (cellIndex > 1 && cell) {
                const cellStr = cell.toString().trim();
                // حذف جميع الأرقام البحتة - الاحتفاظ بالنصوص فقط
                if (/^\d+$/.test(cellStr)) {
                    cleanedData.rows[rowIndex][cellIndex] = '';
                }
            }
        });
    });

    return cleanedData;
}

// دالة لإعادة تطبيق البيانات على خانات جدول المناوبين
function reapplyShiftsDataToInputs() {
    console.log('🔄 إعادة تطبيق بيانات المناوبين على الخانات...');

    if (!shiftsData || !shiftsData.rows) return;

    const shiftsInputs = document.querySelectorAll('.shifts-editable-cell');
    shiftsInputs.forEach((input, index) => {
        const rowIndex = Math.floor(index / (shiftsData.headers.length - 2)); // -2 لتجاهل عمود الرقم والموقع
        const colIndex = (index % (shiftsData.headers.length - 2)) + 2; // +2 لتجاهل عمود الرقم والموقع

        if (shiftsData.rows[rowIndex] && shiftsData.rows[rowIndex][colIndex]) {
            const value = shiftsData.rows[rowIndex][colIndex];
            if (value) {
                const valueStr = value.toString().trim();
                // إظهار النصوص فقط (ليس أرقام بحتة)
                if (!/^\d+$/.test(valueStr)) {
                    input.value = value;
                    console.log(`📝 تم تطبيق القيمة "${value}" على الخانة [${rowIndex}, ${colIndex}]`);
                }
            }
        }
    });
}

// إضافة عمود جديد في جدول المناوبين
function addShiftsColumn() {
    shiftsData.headers.push('عمود جديد');

    // إضافة خلية فارغة لكل صف
    shiftsData.rows.forEach(row => {
        row.push('');
    });

    generateShiftsTable();
    saveShiftsDataToServer();
}

// إضافة عمود بعد عمود محدد في جدول المناوبين
function addShiftsColumnAfter(columnIndex) {
    shiftsData.headers.splice(columnIndex + 1, 0, 'عمود جديد');

    // إضافة خلية فارغة لكل صف في الموضع المحدد
    shiftsData.rows.forEach(row => {
        row.splice(columnIndex + 1, 0, '');
    });

    generateShiftsTable();
    saveShiftsDataToServer();
}

// حذف عمود من جدول المناوبين
function deleteShiftsColumn(columnIndex) {
    if (shiftsData.headers.length <= 3) {
        alert('لا يمكن حذف المزيد من الأعمدة');
        return;
    }

    if (confirm('هل أنت متأكد من حذف هذا العمود؟')) {
        shiftsData.headers.splice(columnIndex, 1);
        shiftsData.rows.forEach(row => {
            row.splice(columnIndex, 1);
        });
        generateShiftsTable();
        saveShiftsDataToServer();
    }
}

// إضافة صف جديد في جدول المناوبين
function addShiftsRow() {
    shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));
    generateShiftsTable();
    saveShiftsDataToServer();
}

// إضافة صف بعد صف محدد في جدول المناوبين
function addShiftsRowAfter(rowIndex) {
    shiftsData.rows.splice(rowIndex + 1, 0, Array(shiftsData.headers.length).fill(''));
    generateShiftsTable();
    saveShiftsDataToServer();
}

// حذف صف من جدول المناوبين
function deleteShiftsRow(rowIndex) {
    if (confirm('هل أنت متأكد من حذف هذا الصف؟')) {
        shiftsData.rows.splice(rowIndex, 1);

        // إذا لم تبق أي صفوف، أضف صف فارغ واحد
        if (shiftsData.rows.length === 0) {
            shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));
        }

        generateShiftsTable();
        saveShiftsDataToServer();
    }
}

// تفريغ جدول المناوبين
function resetShiftsTable() {
    if (confirm('هل أنت متأكد من تفريغ جدول المناوبين؟ سيتم الاحتفاظ بالمواقع وإعادة تعيين العناوين للقيم الافتراضية.')) {
        // حفظ المواقع الحالية قبل التفريغ
        const currentShiftsLocations = [];
        const shiftsLocationSelects = document.querySelectorAll('.shifts-location-select');
        shiftsLocationSelects.forEach((select, index) => {
            if (select.value) {
                currentShiftsLocations[index] = select.value;
            }
        });

        // إعادة تعيين البيانات للقيم الافتراضية
        shiftsData.headers = [...DEFAULT_SHIFTS_HEADERS];
        shiftsData.rows = [];

        // إنشاء صف واحد فارغ
        shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));

        // إعادة تطبيق المواقع المحفوظة
        currentShiftsLocations.forEach((locationId, rowIndex) => {
            if (locationId && shiftsData.rows[rowIndex]) {
                shiftsData.rows[rowIndex][1] = locationId; // عمود الموقع هو الثاني
            }
        });

        generateShiftsTable();

        // حفظ البيانات والمواقع في الخادم
        setTimeout(() => {
            saveShiftsDataToServer();
            saveShiftsLocationsToServer();
        }, 100);

        autoSave();
        showAlert('تم تفريغ جدول المناوبين مع الاحتفاظ بالمواقع وإعادة تعيين العناوين', 'success');
    }
}

// حفظ مواقع جدول المناوبين في الخادم مع debouncing
function saveShiftsLocationsToServer() {
    // إلغاء أي حفظ سابق
    if (saveTimeouts.shiftsLocations) {
        clearTimeout(saveTimeouts.shiftsLocations);
    }

    // تعيين مهلة جديدة
    saveTimeouts.shiftsLocations = setTimeout(() => {
        try {
            const currentShiftsLocations = {};
            const shiftsLocationSelects = document.querySelectorAll('.shifts-location-select');

            shiftsLocationSelects.forEach((select, index) => {
                const rowIndex = select.getAttribute('data-row');
                if (select.value && rowIndex !== null) {
                    currentShiftsLocations[rowIndex] = select.value;
                }
            });

            const data = {
                locations: currentShiftsLocations,
                timestamp: new Date().toISOString()
            };

            fetch('/receipts/api/save-shifts-locations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    console.log('✅ تم حفظ مواقع المناوبين في الخادم');
                } else {
                    console.error('❌ فشل في حفظ مواقع المناوبين:', result.error);
                }
            })
            .catch(error => {
                console.error('❌ خطأ في حفظ مواقع المناوبين:', error);
            });
        } catch (error) {
            console.error('❌ خطأ في حفظ مواقع المناوبين:', error);
        }
        saveTimeouts.shiftsLocations = null;
    }, 300); // 300ms debounce
}

// تحميل مواقع جدول المناوبين من الخادم
function loadShiftsLocationsFromServer() {
    console.log('🔄 بدء تحميل مواقع المناوبين من الخادم...');
    return fetch('/receipts/api/get-shifts-locations')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.locations) {
                console.log('📍 مواقع المناوبين المحملة:', data.locations);

                // تطبيق المواقع على الجدول
                Object.entries(data.locations).forEach(([rowIndex, locationId]) => {
                    const select = document.querySelector(`.shifts-location-select[data-row="${rowIndex}"]`);
                    if (select) {
                        select.value = locationId;
                        // تحديث البيانات في الذاكرة
                        updateShiftsCell(parseInt(rowIndex), 1, locationId);
                    }
                });
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل مواقع المناوبين:', error);
        });
}

// تحميل بيانات جدول المناوبين من الخادم
function loadShiftsDataFromServer() {
    console.log('🔄 بدء تحميل بيانات المناوبين من الخادم...');
    return fetch('/receipts/api/get-shifts-data')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.shiftsData) {
                console.log('📊 بيانات المناوبين المحملة:', data.shiftsData);

                // تحديث بيانات المناوبين
                shiftsData = {
                    headers: data.shiftsData.headers || [...DEFAULT_SHIFTS_HEADERS],
                    rows: data.shiftsData.rows || []
                };

                // التأكد من وجود صف واحد على الأقل
                if (shiftsData.rows.length === 0) {
                    shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));
                }

                // إعادة إنشاء جدول المناوبين بالبيانات المحملة
                generateShiftsTable();

                // تحميل مواقع المناوبين وإعادة تطبيق البيانات بعد إنشاء الجدول
                setTimeout(() => {
                    loadShiftsLocationsFromServer();
                    // إعادة تطبيق البيانات النصية على الخانات
                    reapplyShiftsDataToInputs();
                    restoreDataStably();
                }, 300);

                console.log('✅ تم تحميل بيانات المناوبين بنجاح وإعادة إنشاء الجدول');
                return true;
            } else {
                console.log('ℹ️ لا توجد بيانات محفوظة للمناوبين، استخدام البيانات الافتراضية');
                return false;
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل بيانات المناوبين:', error);
            return false;
        });
}

// تحديث قوائم المواقع في جدول المناوبين
function updateShiftsLocationSelects() {
    console.log('🔄 تحديث قوائم المواقع في جدول المناوبين...');
    const shiftsLocationSelects = document.querySelectorAll('.shifts-location-select');
    shiftsLocationSelects.forEach(select => {
        const currentValue = select.value;

        // مسح الخيارات الحالية
        select.innerHTML = '<option value="">اختر الموقع</option>';

        // إضافة المواقع الجديدة
        if (locationsDatabase && locationsDatabase.length > 0) {
            locationsDatabase.forEach(location => {
                const option = document.createElement('option');
                option.value = location.id;
                option.textContent = location.name;
                if (location.id == currentValue) {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        }
    });
}

// إعداد مستمعي الأحداث لجدول المناوبين
function setupShiftsTableEventListeners() {
    console.log('🔧 إعداد مستمعي الأحداث لجدول المناوبين...');

    const shiftsTableBody = document.getElementById('shiftsTableBody');
    if (shiftsTableBody) {
        // إضافة مستمع أحداث للجدول بأكمله لالتقاط التغييرات
        shiftsTableBody.addEventListener('input', function(event) {
            if (event.target.classList.contains('editable-cell')) {
                const rowIndex = parseInt(event.target.closest('tr').rowIndex) - 1;
                const colIndex = Array.from(event.target.closest('tr').children).indexOf(event.target.closest('td'));
                updateShiftsCell(rowIndex, colIndex, event.target.value);
            }
        }, true);

        console.log('✅ تم إعداد مستمعي الأحداث لجدول المناوبين');
    } else {
        console.warn('⚠️ لم يتم العثور على عنصر shiftsTableBody');
    }
}

from flask import Blueprint, render_template, request, jsonify, session, send_file, make_response
from flask_login import login_required, current_user
from datetime import datetime
import json
from io import BytesIO
import csv
import tempfile
import os
from db import db
from models import ReceiptData, ReceiptLocations, PatrolData, PatrolLocations, ShiftsData, ShiftsLocations

# محاولة استيراد مكتبات Excel
try:
    import pandas as pd
    import xlsxwriter
    EXCEL_AVAILABLE = True
    print("✅ مكتبات Excel متوفرة")
except ImportError:
    EXCEL_AVAILABLE = False
    print("⚠️ مكتبات Excel غير متوفرة، سيتم استخدام CSV كبديل")

# إنشاء blueprint مع تعطيل CSRF للـ APIs
receipts_bp = Blueprint('receipts', __name__, url_prefix='/receipts')

@receipts_bp.route('/')
@login_required
def index():
    """صفحة كشف الاستلامات الرئيسية"""
    return render_template('receipts/index.html')

@receipts_bp.route('/save', methods=['POST'])
@login_required
def save_receipt():
    """حفظ بيانات كشف الاستلامات"""
    try:
        data = request.get_json()

        # التحقق من صحة البيانات
        if not data:
            return jsonify({
                'success': False,
                'message': 'لا توجد بيانات للحفظ'
            })

        # حفظ البيانات في الجلسة مؤقتاً (يمكن تطويرها لحفظ في قاعدة البيانات)
        session_key = f"receipt_{current_user.id}_{datetime.now().strftime('%Y%m%d')}"
        session[session_key] = {
            'hijri_date': data.get('hijriDate', ''),
            'gregorian_date': data.get('gregorianDate', datetime.now().strftime('%Y-%m-%d')),
            'receipt_number': data.get('receiptNumber', ''),
            'main_rows': data.get('mainRows', []),
            'patrol_notes': data.get('patrolNotes', []),
            'guard_notes': data.get('guardNotes', []),
            'locations': data.get('locations', []),
            'personnel': data.get('personnel', []),
            'created_by': current_user.username,
            'created_at': datetime.now().isoformat(),
            'last_modified': datetime.now().isoformat()
        }

        return jsonify({
            'success': True,
            'message': 'تم حفظ كشف الاستلامات بنجاح',
            'receipt_number': data.get('receiptNumber', ''),
            'saved_at': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في حفظ البيانات: {str(e)}'})

@receipts_bp.route('/load')
@login_required
def load_receipt():
    """تحميل بيانات كشف الاستلامات المحفوظة"""
    try:
        session_key = f"receipt_{current_user.id}_{datetime.now().strftime('%Y%m%d')}"
        data = session.get(session_key, {})
        
        if not data:
            # إنشاء بيانات افتراضية
            data = {
                'title': 'كشف استلام المناوبات',
                'date': datetime.now().strftime('%Y-%m-%d'),
                'time': datetime.now().strftime('%H:%M'),
                'columns': [
                    {'id': 'location', 'title': 'الموقع', 'editable': True},
                    {'id': 'morning', 'title': 'الصباح', 'editable': True},
                    {'id': 'afternoon', 'title': 'المساء', 'editable': True},
                    {'id': 'received', 'title': 'الاستلام', 'editable': True},
                    {'id': 'time_received', 'title': 'الوقت', 'editable': True},
                    {'id': 'col1', 'title': 'عمود جديد', 'editable': True},
                    {'id': 'col2', 'title': 'عمود جديد', 'editable': True},
                    {'id': 'col3', 'title': 'عمود جديد', 'editable': True},
                    {'id': 'col4', 'title': 'عمود جديد', 'editable': True},
                    {'id': 'col5', 'title': 'عمود جديد', 'editable': True}
                ],
                'rows': [
                    {'id': 1, 'location': 'اسم الموقع', 'morning': 'اسم الفرد', 'afternoon': 'اسم الفرد', 'received': 'اسم الفرد', 'time_received': '06:00 ص', 'col1': '', 'col2': '', 'col3': '', 'col4': '', 'col5': ''},
                    {'id': 2, 'location': 'اسم الموقع', 'morning': 'اسم الفرد', 'afternoon': 'اسم الفرد', 'received': 'اسم الفرد', 'time_received': '06:00 ص', 'col1': '', 'col2': '', 'col3': '', 'col4': '', 'col5': ''},
                    {'id': 3, 'location': 'اسم الموقع', 'morning': 'اسم الفرد', 'afternoon': 'اسم الفرد', 'received': 'اسم الفرد', 'time_received': '06:00 ص', 'col1': '', 'col2': '', 'col3': '', 'col4': '', 'col5': ''},
                    {'id': 4, 'location': 'اسم الموقع', 'morning': 'اسم الفرد', 'afternoon': 'اسم الفرد', 'received': 'اسم الفرد', 'time_received': '06:00 ص', 'col1': '', 'col2': '', 'col3': '', 'col4': '', 'col5': ''},
                    {'id': 5, 'location': 'اسم الموقع', 'morning': 'اسم الفرد', 'afternoon': 'اسم الفرد', 'received': 'اسم الفرد', 'time_received': '06:00 ص', 'col1': '', 'col2': '', 'col3': '', 'col4': '', 'col5': ''},
                    {'id': 6, 'location': 'اسم الموقع', 'morning': 'اسم الفرد', 'afternoon': 'اسم الفرد', 'received': 'اسم الفرد', 'time_received': '06:00 ص', 'col1': '', 'col2': '', 'col3': '', 'col4': '', 'col5': ''}
                ],
                'patrol_notes': 'أدخل ملاحظات الدوريات هنا... يمكن كتابة تفاصيل الدوريات والملاحظات...',
                'shift_notes': 'أدخل ملاحظات المناوبين هنا... يمكن كتابة تفاصيل المناوبات والملاحظات...'
            }
        
        return jsonify({'success': True, 'data': data})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في تحميل البيانات: {str(e)}'})

@receipts_bp.route('/personnel/search')
@login_required
def search_personnel():
    """البحث عن الأفراد بالهوية الوطنية"""
    try:
        national_id = request.args.get('national_id', '').strip()

        if not national_id:
            return jsonify({'success': False, 'message': 'رقم الهوية الوطنية مطلوب'})

        if len(national_id) < 10:
            return jsonify({'success': False, 'message': 'رقم الهوية غير مكتمل'})

        # البحث في قاعدة البيانات الحقيقية
        from models import Personnel

        # السماح لجميع المستخدمين بالبحث في جميع الأفراد في كشف الاستلامات
        personnel = Personnel.query.filter_by(phone=national_id).first()
        if not personnel:
            personnel = Personnel.query.filter_by(personnel_id=national_id).first()

        if personnel:
            # تحديد رقم الهوية الوطنية - إما من phone أو personnel_id
            national_id_value = personnel.phone if personnel.phone and len(personnel.phone) == 10 else personnel.personnel_id

            return jsonify({
                'success': True,
                'personnel': {
                    'national_id': national_id_value,
                    'name': personnel.name,
                    'rank': personnel.rank,
                    'unit': personnel.warehouse.name if personnel.warehouse else 'غير محدد'
                }
            })
        else:
            return jsonify({'success': False, 'message': 'لم يتم العثور على الفرد'})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في البحث: {str(e)}'})

@receipts_bp.route('/locations/list')
@login_required
def list_locations():
    """قائمة المواقع المتاحة"""
    try:
        # بيانات وهمية للمواقع (في التطبيق الحقيقي ستأتي من قاعدة البيانات)
        locations_database = [
            {'id': 1, 'name': 'البوابة الرئيسية الشمالية', 'type': 'أمني'},
            {'id': 2, 'name': 'البوابة الجنوبية', 'type': 'أمني'},
            {'id': 3, 'name': 'برج المراقبة الشرقي', 'type': 'مراقبة'},
            {'id': 4, 'name': 'المبنى الإداري', 'type': 'إداري'},
            {'id': 5, 'name': 'موقف السيارات', 'type': 'حراسة'}
        ]

        return jsonify({'success': True, 'locations': locations_database})

    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في تحميل المواقع: {str(e)}'})

@receipts_bp.route('/export-excel', methods=['POST'])
@login_required
def export_excel():
    """تصدير كشف الاستلامات إلى Excel أو CSV"""
    try:
        print("📥 تم استلام طلب تصدير")
        data = request.get_json()
        print(f"📦 البيانات المستلمة: {data}")

        if not data:
            print("❌ لا توجد بيانات JSON")
            return jsonify({'success': False, 'message': 'لا توجد بيانات للتصدير'})

        # استخراج البيانات
        receipt_info = data.get('receipt_info', {})
        main_table = data.get('main_table', [])
        patrol_table = data.get('patrol_table', [])
        shifts_table = data.get('shifts_table', [])

        print(f"📋 معلومات الكشف: {receipt_info}")
        print(f"📊 الجدول الرئيسي: {len(main_table)} صف")
        print(f"📊 جدول الدوريات: {len(patrol_table)} صف")
        print(f"📊 كشف المناوبين: {len(shifts_table)} صف")

        # إنشاء اسم الملف
        gregorian_date = receipt_info.get('gregorian_date', datetime.now().strftime('%Y-%m-%d'))
        filename = f"كشف_الاستلامات_{gregorian_date}"
        print(f"📁 اسم الملف: {filename}")

        # محاولة إنشاء ملف Excel أولاً
        if EXCEL_AVAILABLE:
            try:
                excel_file = create_receipt_excel_with_pandas(main_table, patrol_table, shifts_table, receipt_info, filename)
                if excel_file:
                    print("✅ تم إنشاء ملف Excel بنجاح")
                    return send_file(
                        excel_file,
                        download_name=f"{filename}.xlsx",
                        as_attachment=True,
                        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    )
            except Exception as e:
                print(f"❌ فشل في إنشاء Excel: {str(e)}")

        # إنشاء ملف CSV كبديل
        print("📄 إنشاء ملف CSV كبديل...")
        csv_file = create_receipt_csv(main_table, patrol_table, shifts_table, receipt_info, filename)

        if csv_file:
            print("✅ تم إنشاء ملف CSV بنجاح")
            return send_file(
                csv_file,
                download_name=f"{filename}.csv",
                as_attachment=True,
                mimetype='text/csv; charset=utf-8'
            )
        else:
            print("❌ فشل في إنشاء ملف CSV")
            return jsonify({'success': False, 'message': 'فشل في إنشاء الملف'})

    except Exception as e:
        print(f"❌ خطأ في التصدير: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': f'خطأ في التصدير: {str(e)}'})

def create_receipt_excel_with_pandas(main_table, patrol_table, shifts_table, receipt_info, filename):
    """إنشاء ملف Excel باستخدام pandas و xlsxwriter مع التنسيق المطلوب"""
    try:
        if not EXCEL_AVAILABLE:
            return None

        print(f"📊 بدء إنشاء ملف Excel: {filename}")

        # إنشاء buffer في الذاكرة
        output = BytesIO()

        # إنشاء workbook
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})

        # تنسيقات الخلايا المحسنة
        # تنسيق معلومات التاريخ في الأعلى
        date_header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#FFFFFF',
            'border': 1,
            'font_size': 12,
            'font_name': 'Arial'
        })

        # تنسيق عناوين الجداول
        table_title_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#D7E4BC',
            'border': 1,
            'font_size': 12,
            'font_name': 'Arial'
        })

        # تنسيق رؤوس الأعمدة
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#D7E4BC',
            'border': 1,
            'font_size': 10,
            'font_name': 'Arial'
        })

        # تنسيق الخلايا العادية
        cell_format = workbook.add_format({
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'border': 1,
            'font_size': 10,
            'font_name': 'Arial'
        })

        # تنسيق خلايا الأرقام
        number_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#F0F0F0',
            'border': 1,
            'font_size': 10,
            'font_name': 'Arial'
        })

        # إنشاء ورقة واحدة تحتوي على كلا الجدولين
        worksheet = workbook.add_worksheet('كشف الاستلامات والدوريات')
        worksheet.right_to_left()

        row = 0

        # ===== معلومات التاريخ في الأعلى (نفس تنسيق الصورة) =====
        # الصف الأول: التاريخ الميلادي والتاريخ الهجري
        gregorian_date = receipt_info.get('gregorian_date', '2025-06-12')
        hijri_date = receipt_info.get('hijri_date', '16 ذو الحجة 1446هـ')
        receipt_number = receipt_info.get('receipt_number', '20250613-615')
        day_name = receipt_info.get('day_name', 'الجمعة')

        worksheet.write(row, 0, gregorian_date, date_header_format)
        worksheet.write(row, 1, 'التاريخ الميلادي', date_header_format)
        worksheet.write(row, 2, hijri_date, date_header_format)
        worksheet.write(row, 3, 'التاريخ الهجري', date_header_format)

        # الصف الثاني: رقم الكشف واليوم
        row += 1
        worksheet.write(row, 0, receipt_number, date_header_format)
        worksheet.write(row, 1, 'رقم الكشف', date_header_format)
        worksheet.write(row, 2, day_name, date_header_format)
        worksheet.write(row, 3, 'اليوم', date_header_format)

        row += 2  # مسافة أقل بين المعلومات والجداول

        # ===== قسم كشف الاستلامات =====
        if main_table and len(main_table) > 0:
            # عنوان كشف الاستلامات
            max_cols = max(len(main_table[0]) if main_table else 8, 8)
            worksheet.merge_range(row, 0, row, max_cols - 1,
                                'كشف الاستلامات', table_title_format)
            row += 1

            # كتابة بيانات كشف الاستلامات
            for row_idx, table_row in enumerate(main_table):
                for col_idx, cell_value in enumerate(table_row):
                    if row_idx == 0:  # صف العناوين
                        format_to_use = header_format
                    elif col_idx == 0 and row_idx > 0:  # عمود الأرقام (ليس العنوان)
                        format_to_use = number_format
                        # التأكد من أن الرقم يظهر بشكل صحيح
                        if not cell_value or cell_value == '':
                            cell_value = str(row_idx)  # استخدام رقم الصف
                    else:  # الخلايا العادية
                        format_to_use = cell_format

                    worksheet.write(row + row_idx, col_idx, str(cell_value) if cell_value else '', format_to_use)

            row += len(main_table) + 1  # مسافة أقل بين الجداول

        # ===== قسم ملاحظات الدوريات =====
        # التأكد من وجود بيانات الدوريات أو إنشاء عناوين افتراضية
        if not patrol_table or len(patrol_table) == 0:
            patrol_table = [['الرقم', 'موقع الاستلام', 'ملاحظات', '12 ظهراً إلى 6 مساءً', '6 صباحاً إلى 12 ظهراً', '12 ليلاً إلى 6 صباحاً', '6 مساءً إلى 12 ليلاً']]

        # عنوان كشف استلامات الدوريات
        max_cols = max(len(main_table[0]) if main_table else 6,
                      len(patrol_table[0]) if patrol_table else 6, 6)
        worksheet.merge_range(row, 0, row, max_cols - 1,
                            'كشف استلامات الدوريات', table_title_format)
        row += 1

        # كتابة بيانات ملاحظات الدوريات
        for row_idx, table_row in enumerate(patrol_table):
            for col_idx, cell_value in enumerate(table_row):
                if row_idx == 0:  # صف العناوين
                    format_to_use = header_format
                elif col_idx == 0 and row_idx > 0:  # عمود الأرقام (ليس العنوان)
                    format_to_use = number_format
                    # التأكد من أن الرقم يظهر بشكل صحيح
                    if not cell_value or cell_value == '':
                        cell_value = str(row_idx)  # استخدام رقم الصف
                else:  # الخلايا العادية
                    format_to_use = cell_format

                worksheet.write(row + row_idx, col_idx, str(cell_value) if cell_value else '', format_to_use)

        # إضافة صفوف فارغة إذا لم تكن موجودة (لتطابق الصورة)
        if len(patrol_table) <= 1:
            for i in range(1, 7):  # إضافة 6 صفوف فارغة
                worksheet.write(row + i, 0, str(i), number_format)  # رقم الصف
                for col in range(1, max_cols):
                    worksheet.write(row + i, col, '', cell_format)

        row += max(len(patrol_table), 7) + 1  # مسافة أقل بين الجداول

        # ===== قسم كشف المناوبين =====
        # التأكد من وجود بيانات المناوبين أو إنشاء عناوين افتراضية
        if not shifts_table or len(shifts_table) == 0:
            shifts_table = [['الرقم', 'موقع الاستلام', 'ملاحظات المناوبين', '6 صباحاً إلى 2 ظهراً', '10 ليلاً إلى 6 صباحاً', '2 ظهراً إلى 10 ليلاً']]

        # عنوان كشف المناوبين
        max_cols = max(len(main_table[0]) if main_table else 6,
                      len(patrol_table[0]) if patrol_table else 6,
                      len(shifts_table[0]) if shifts_table else 6, 6)
        worksheet.merge_range(row, 0, row, max_cols - 1,
                            'كشف المناوبين', table_title_format)
        row += 1

        # كتابة بيانات كشف المناوبين
        for row_idx, table_row in enumerate(shifts_table):
            for col_idx, cell_value in enumerate(table_row):
                if row_idx == 0:  # صف العناوين
                    format_to_use = header_format
                elif col_idx == 0 and row_idx > 0:  # عمود الأرقام (ليس العنوان)
                    format_to_use = number_format
                    # التأكد من أن الرقم يظهر بشكل صحيح
                    if not cell_value or cell_value == '':
                        cell_value = str(row_idx)  # استخدام رقم الصف
                else:  # الخلايا العادية
                    format_to_use = cell_format

                worksheet.write(row + row_idx, col_idx, str(cell_value) if cell_value else '', format_to_use)

        # إضافة صفوف فارغة إذا لم تكن موجودة (لتطابق الصورة)
        if len(shifts_table) <= 1:
            for i in range(1, 7):  # إضافة 6 صفوف فارغة
                worksheet.write(row + i, 0, str(i), number_format)  # رقم الصف
                for col in range(1, max_cols):
                    worksheet.write(row + i, col, '', cell_format)

        # تعديل عرض الأعمدة للورقة الكاملة
        for col_num in range(max_cols):
            if col_num == 0:  # عمود الأرقام أضيق
                worksheet.set_column(col_num, col_num, 8)
            elif col_num == 1:  # عمود الموقع
                worksheet.set_column(col_num, col_num, 25)
            else:  # باقي الأعمدة
                worksheet.set_column(col_num, col_num, 18)

        # تعيين ارتفاع الصفوف
        worksheet.set_default_row(20)  # ارتفاع أقل للصفوف

        # تجميد الصفوف العلوية (معلومات التاريخ)
        worksheet.freeze_panes(3, 0)

        # إعداد الطباعة
        worksheet.set_landscape()  # اتجاه أفقي
        worksheet.set_paper(9)  # حجم A4
        worksheet.fit_to_pages(1, 0)  # ملائمة العرض في صفحة واحدة
        worksheet.set_margins(0.3, 0.3, 0.5, 0.5)  # هوامش أصغر

        # إغلاق الـ workbook
        workbook.close()
        output.seek(0)

        print(f"✅ تم إنشاء ملف Excel بنجاح، الحجم: {len(output.getvalue())} بايت")
        return output

    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف Excel: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def create_receipt_csv(main_table, patrol_table, shifts_table, receipt_info, filename):
    """إنشاء ملف CSV كبديل"""
    try:
        print(f"📄 بدء إنشاء ملف CSV: {filename}")

        # إنشاء ملف مؤقت
        temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8-sig')

        writer = csv.writer(temp_file)

        # كتابة معلومات الكشف
        writer.writerow(['كشف الاستلامات وكشف استلامات الدوريات - الفريق الأمني'])
        writer.writerow(['=' * 60])
        writer.writerow([])

        if receipt_info.get('hijri_date'):
            writer.writerow(['التاريخ الهجري:', receipt_info['hijri_date']])
        if receipt_info.get('gregorian_date'):
            writer.writerow(['التاريخ الميلادي:', receipt_info['gregorian_date']])
        if receipt_info.get('day_name'):
            writer.writerow(['اليوم:', receipt_info['day_name']])
        if receipt_info.get('receipt_number'):
            writer.writerow(['رقم الكشف:', receipt_info['receipt_number']])

        writer.writerow([])
        writer.writerow(['كشف الاستلامات'])
        writer.writerow(['-' * 30])

        # كتابة الجدول الرئيسي
        if main_table and len(main_table) > 0:
            for row in main_table:
                writer.writerow([str(cell) if cell else '' for cell in row])
        else:
            writer.writerow(['لا توجد بيانات في كشف الاستلامات'])

        writer.writerow([])
        writer.writerow([])
        writer.writerow(['كشف استلامات الدوريات'])
        writer.writerow(['-' * 30])

        # كتابة جدول الدوريات
        if patrol_table and len(patrol_table) > 0:
            for row in patrol_table:
                writer.writerow([str(cell) if cell else '' for cell in row])
        else:
            writer.writerow(['لا توجد بيانات في جدول كشف استلامات الدوريات'])

        writer.writerow([])
        writer.writerow([])
        writer.writerow(['كشف المناوبين'])
        writer.writerow(['-' * 30])

        # كتابة كشف المناوبين
        if shifts_table and len(shifts_table) > 0:
            for row in shifts_table:
                writer.writerow([str(cell) if cell else '' for cell in row])
        else:
            writer.writerow(['لا توجد بيانات في كشف المناوبين'])

        temp_file.close()

        print(f"✅ تم إنشاء ملف CSV بنجاح: {temp_file.name}")
        return temp_file.name

    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف CSV: {str(e)}")
        return None

def create_receipt_excel(sheets_data, receipt_info, filename):
    """إنشاء ملف Excel لكشف الاستلامات"""
    try:
        print(f"📊 بدء إنشاء ملف Excel: {filename}")
        print(f"📋 معلومات الكشف: {receipt_info}")
        print(f"📊 عدد الأوراق: {len(sheets_data)}")

        # إنشاء buffer في الذاكرة
        output = BytesIO()

        # إنشاء workbook
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})

        # تنسيقات الخلايا
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#D7E4BC',
            'border': 1,
            'font_size': 12
        })

        cell_format = workbook.add_format({
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'border': 1,
            'font_size': 11
        })

        info_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'right',
            'bg_color': '#F2F2F2',
            'border': 1,
            'font_size': 12
        })

        title_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#4472C4',
            'font_color': 'white',
            'border': 1,
            'font_size': 14
        })

        # معالجة كل sheet
        for sheet_name, df in sheets_data.items():
            print(f"📄 إنشاء ورقة: {sheet_name}")
            worksheet = workbook.add_worksheet(sheet_name)

            # تعيين اتجاه الكتابة من اليمين لليسار
            worksheet.right_to_left()

            # إضافة معلومات الكشف في الأعلى
            row = 0

            # عنوان رئيسي
            worksheet.merge_range(row, 0, row, max(3, len(df.columns) - 1 if not df.empty else 3),
                                'كشف الاستلامات - الفريق الأمني', title_format)
            row += 2

            # معلومات الكشف
            if receipt_info.get('hijri_date'):
                worksheet.write(row, 0, 'التاريخ الهجري:', info_format)
                worksheet.write(row, 1, receipt_info['hijri_date'], cell_format)
                if receipt_info.get('gregorian_date'):
                    worksheet.write(row, 2, 'التاريخ الميلادي:', info_format)
                    worksheet.write(row, 3, receipt_info['gregorian_date'], cell_format)
                row += 1

            if receipt_info.get('day_name'):
                worksheet.write(row, 0, 'اليوم:', info_format)
                worksheet.write(row, 1, receipt_info['day_name'], cell_format)
                if receipt_info.get('receipt_number'):
                    worksheet.write(row, 2, 'رقم الكشف:', info_format)
                    worksheet.write(row, 3, receipt_info['receipt_number'], cell_format)
                row += 1

            # إضافة مسافة
            row += 2

            # إضافة البيانات
            if not df.empty and len(df.columns) > 0:
                print(f"📊 كتابة البيانات: {len(df)} صف، {len(df.columns)} عمود")

                # كتابة العناوين
                for col_num, column in enumerate(df.columns):
                    worksheet.write(row, col_num, str(column), header_format)

                # كتابة البيانات
                for row_num, (index, data_row) in enumerate(df.iterrows(), start=row + 1):
                    for col_num, value in enumerate(data_row):
                        cell_value = str(value) if pd.notna(value) and value != '' else ''
                        worksheet.write(row_num, col_num, cell_value, cell_format)

                # تعديل عرض الأعمدة
                for col_num in range(len(df.columns)):
                    worksheet.set_column(col_num, col_num, 18)
            else:
                print(f"⚠️ لا توجد بيانات في ورقة {sheet_name}")
                worksheet.write(row, 0, 'لا توجد بيانات', cell_format)

        # إغلاق الـ workbook
        workbook.close()
        output.seek(0)

        print(f"✅ تم إنشاء ملف Excel بنجاح، الحجم: {len(output.getvalue())} بايت")
        return output

    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف Excel: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

@receipts_bp.route('/templates')
@login_required
def get_templates():
    """الحصول على قوالب جاهزة لكشف الاستلامات"""
    templates = [
        {
            'id': 'security_shifts',
            'name': 'مناوبات الأمن',
            'description': 'قالب لكشف استلام مناوبات الأمن',
            'columns': [
                {'id': 'location', 'title': 'الموقع', 'editable': True},
                {'id': 'morning_shift', 'title': 'المناوبة الصباحية', 'editable': True},
                {'id': 'evening_shift', 'title': 'المناوبة المسائية', 'editable': True},
                {'id': 'night_shift', 'title': 'المناوبة الليلية', 'editable': True},
                {'id': 'received_by', 'title': 'استلم بواسطة', 'editable': True},
                {'id': 'time', 'title': 'الوقت', 'editable': True}
            ]
        },
        {
            'id': 'equipment_handover',
            'name': 'تسليم المعدات',
            'description': 'قالب لكشف تسليم واستلام المعدات',
            'columns': [
                {'id': 'equipment', 'title': 'المعدة', 'editable': True},
                {'id': 'serial_number', 'title': 'الرقم التسلسلي', 'editable': True},
                {'id': 'condition', 'title': 'الحالة', 'editable': True},
                {'id': 'handed_by', 'title': 'سلم بواسطة', 'editable': True},
                {'id': 'received_by', 'title': 'استلم بواسطة', 'editable': True},
                {'id': 'date_time', 'title': 'التاريخ والوقت', 'editable': True}
            ]
        }
    ]
    
    return jsonify({'success': True, 'templates': templates})



@receipts_bp.route('/api/save-receipt-locations', methods=['POST'])
def save_receipt_locations():
    """حفظ مواقع كشف الاستلامات في قاعدة البيانات"""
    try:
        print("📥 تم استلام طلب حفظ المواقع")
        data = request.get_json()
        print(f"📦 البيانات المستلمة: {data}")

        if not data:
            print("❌ لا توجد بيانات JSON")
            return jsonify({'success': False, 'error': 'لا توجد بيانات JSON'})

        locations = data.get('locations', {})
        timestamp = data.get('timestamp')
        print(f"📍 المواقع: {locations}")
        print(f"⏰ الوقت: {timestamp}")

        # حذف المواقع السابقة (مشتركة لجميع المستخدمين)
        ReceiptLocations.query.delete()

        # حفظ المواقع الجديدة (مشتركة لجميع المستخدمين)
        # استخدام created_by = 1 للمستخدم الافتراضي
        for row_index, location_id in locations.items():
            receipt_location = ReceiptLocations(
                row_index=int(row_index),
                location_id=location_id,
                timestamp=timestamp,
                created_by=current_user.id if current_user.is_authenticated else 1
            )
            db.session.add(receipt_location)

        db.session.commit()

        print("✅ تم حفظ المواقع بنجاح")
        return jsonify({'success': True, 'message': 'تم حفظ المواقع بنجاح'})
    except Exception as e:
        print(f"❌ خطأ في حفظ المواقع: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@receipts_bp.route('/api/get-receipt-locations', methods=['GET'])
def get_receipt_locations():
    """استرجاع مواقع كشف الاستلامات من قاعدة البيانات"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        # تحميل المواقع المشتركة (created_by = 0)
        cursor.execute("""
            SELECT row_index, location_id
            FROM receipt_locations
            WHERE created_by = 0
            ORDER BY row_index
        """)

        locations = {}
        for row in cursor.fetchall():
            locations[str(row[0])] = row[1]

        conn.close()
        return jsonify({'success': True, 'locations': locations})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@receipts_bp.route('/api/save-patrol-locations', methods=['POST'])
def save_patrol_locations():
    """حفظ مواقع جدول ملاحظات الدوريات في قاعدة البيانات"""
    try:
        print("📥 تم استلام طلب حفظ مواقع الدوريات")
        data = request.get_json()
        print(f"📦 البيانات المستلمة: {data}")

        if not data:
            print("❌ لا توجد بيانات JSON")
            return jsonify({'success': False, 'error': 'لا توجد بيانات JSON'})

        locations = data.get('locations', {})
        timestamp = data.get('timestamp')
        print(f"📍 مواقع الدوريات: {locations}")
        print(f"⏰ الوقت: {timestamp}")

        conn = get_db_connection()
        cursor = conn.cursor()

        # إنشاء جدول مواقع الدوريات إذا لم يكن موجوداً
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patrol_locations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                row_index INTEGER NOT NULL,
                location_id TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                created_by INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # حذف المواقع السابقة (مشتركة لجميع المستخدمين)
        cursor.execute("DELETE FROM patrol_locations")

        # حفظ المواقع الجديدة (مشتركة لجميع المستخدمين)
        # استخدام created_by = 0 للإشارة إلى البيانات المشتركة
        for row_index, location_id in locations.items():
            cursor.execute("""
                INSERT INTO patrol_locations (row_index, location_id, timestamp, created_by)
                VALUES (?, ?, ?, ?)
            """, (int(row_index), location_id, timestamp, 0))

        conn.commit()
        conn.close()
        print("✅ تم حفظ مواقع الدوريات بنجاح")
        return jsonify({'success': True, 'message': 'تم حفظ مواقع الدوريات بنجاح'})
    except Exception as e:
        print(f"❌ خطأ في حفظ مواقع الدوريات: {e}")
        return jsonify({'success': False, 'error': str(e)})

@receipts_bp.route('/api/get-patrol-locations', methods=['GET'])
def get_patrol_locations():
    """استرجاع مواقع جدول ملاحظات الدوريات من قاعدة البيانات"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # إنشاء جدول مواقع الدوريات إذا لم يكن موجوداً
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patrol_locations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                row_index INTEGER NOT NULL,
                location_id TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                created_by INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # تحميل المواقع المشتركة (created_by = 0)
        cursor.execute("""
            SELECT row_index, location_id
            FROM patrol_locations
            WHERE created_by = 0
            ORDER BY row_index
        """)

        locations = {}
        for row in cursor.fetchall():
            locations[str(row[0])] = row[1]

        conn.close()
        return jsonify({'success': True, 'locations': locations})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@receipts_bp.route('/api/save-receipt-data', methods=['POST'])
def save_receipt_data():
    """حفظ جميع بيانات كشف الاستلامات في قاعدة البيانات"""
    try:
        print("📥 تم استلام طلب حفظ البيانات")
        data = request.get_json()
        print(f"📦 البيانات المستلمة: {data}")

        if not data:
            print("❌ لا توجد بيانات JSON")
            return jsonify({'success': False, 'error': 'لا توجد بيانات JSON'})

        # حذف البيانات السابقة (مشتركة لجميع المستخدمين)
        ReceiptData.query.delete()

        # حفظ البيانات الجديدة (مشتركة لجميع المستخدمين)
        receipt_data = ReceiptData(
            user_id=current_user.id if current_user.is_authenticated else 1,
            receipt_data=json.dumps(data, ensure_ascii=False)
        )
        db.session.add(receipt_data)
        db.session.commit()

        print("✅ تم حفظ البيانات بنجاح")
        return jsonify({'success': True, 'message': 'تم حفظ البيانات بنجاح'})
    except Exception as e:
        print(f"❌ خطأ في حفظ البيانات: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@receipts_bp.route('/api/save-patrol-data', methods=['POST'])
def save_patrol_data():
    """حفظ بيانات جدول ملاحظات الدوريات في قاعدة البيانات"""
    try:
        print("📥 تم استلام طلب حفظ بيانات الدوريات")
        data = request.get_json()
        print(f"📦 بيانات الدوريات المستلمة: {data}")

        if not data:
            print("❌ لا توجد بيانات JSON")
            return jsonify({'success': False, 'error': 'لا توجد بيانات JSON'})

        # حذف البيانات السابقة (مشتركة لجميع المستخدمين)
        PatrolData.query.delete()

        # حفظ البيانات الجديدة (مشتركة لجميع المستخدمين)
        patrol_data = PatrolData(
            user_id=current_user.id if current_user.is_authenticated else 1,
            patrol_data=json.dumps(data, ensure_ascii=False)
        )
        db.session.add(patrol_data)
        db.session.commit()

        print("✅ تم حفظ بيانات الدوريات بنجاح")
        return jsonify({'success': True, 'message': 'تم حفظ بيانات الدوريات بنجاح'})
    except Exception as e:
        print(f"❌ خطأ في حفظ بيانات الدوريات: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@receipts_bp.route('/api/get-receipt-data', methods=['GET'])
def get_receipt_data():
    """استرجاع جميع بيانات كشف الاستلامات من قاعدة البيانات"""
    try:
        # تحميل أحدث البيانات
        receipt_data = ReceiptData.query.order_by(ReceiptData.updated_at.desc()).first()

        if receipt_data:
            data = json.loads(receipt_data.receipt_data)
            return jsonify({'success': True, 'data': data})
        else:
            return jsonify({'success': False, 'message': 'لا توجد بيانات محفوظة'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@receipts_bp.route('/api/get-patrol-data', methods=['GET'])
def get_patrol_data():
    """استرجاع بيانات جدول ملاحظات الدوريات من قاعدة البيانات"""
    try:
        # تحميل أحدث البيانات
        patrol_data = PatrolData.query.order_by(PatrolData.updated_at.desc()).first()

        if patrol_data:
            data = json.loads(patrol_data.patrol_data)
            print("✅ تم تحميل بيانات الدوريات من قاعدة البيانات")
            return jsonify({'success': True, 'data': data})
        else:
            print("ℹ️ لا توجد بيانات دوريات محفوظة")
            return jsonify({'success': False, 'message': 'لا توجد بيانات دوريات محفوظة'})

    except Exception as e:
        print(f"❌ خطأ في تحميل بيانات الدوريات: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@receipts_bp.route('/api/save-shifts-locations', methods=['POST'])
def save_shifts_locations():
    """حفظ مواقع كشف المناوبين في قاعدة البيانات"""
    try:
        print("📥 تم استلام طلب حفظ مواقع المناوبين")
        data = request.get_json()
        print(f"📦 البيانات المستلمة: {data}")

        if not data:
            print("❌ لا توجد بيانات JSON")
            return jsonify({'success': False, 'error': 'لا توجد بيانات JSON'})

        locations = data.get('locations', {})
        timestamp = data.get('timestamp')
        print(f"📍 مواقع المناوبين: {locations}")
        print(f"⏰ الوقت: {timestamp}")

        # حذف المواقع السابقة (مشتركة لجميع المستخدمين)
        ShiftsLocations.query.delete()

        # حفظ المواقع الجديدة (مشتركة لجميع المستخدمين)
        for row_index, location_id in locations.items():
            shifts_location = ShiftsLocations(
                row_index=int(row_index),
                location_id=location_id,
                timestamp=timestamp,
                created_by=0  # مشتركة لجميع المستخدمين
            )
            db.session.add(shifts_location)

        db.session.commit()
        print("✅ تم حفظ مواقع المناوبين بنجاح")
        return jsonify({'success': True, 'message': 'تم حفظ مواقع المناوبين بنجاح'})
    except Exception as e:
        print(f"❌ خطأ في حفظ مواقع المناوبين: {e}")
        return jsonify({'success': False, 'error': str(e)})

@receipts_bp.route('/api/get-shifts-locations', methods=['GET'])
def get_shifts_locations():
    """استرجاع مواقع كشف المناوبين من قاعدة البيانات"""
    try:
        # تحميل المواقع المشتركة (created_by = 0)
        shifts_locations = ShiftsLocations.query.filter_by(created_by=0).order_by(ShiftsLocations.row_index).all()

        locations = {}
        for location in shifts_locations:
            locations[str(location.row_index)] = location.location_id
        return jsonify({'success': True, 'locations': locations})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@receipts_bp.route('/api/save-shifts-data', methods=['POST'])
def save_shifts_data():
    """حفظ بيانات كشف المناوبين في قاعدة البيانات"""
    try:
        print("📥 تم استلام طلب حفظ بيانات المناوبين")
        data = request.get_json()
        print(f"📦 بيانات المناوبين المستلمة: {data}")

        if not data:
            print("❌ لا توجد بيانات JSON")
            return jsonify({'success': False, 'error': 'لا توجد بيانات JSON'})

        # حذف البيانات السابقة (مشتركة لجميع المستخدمين)
        ShiftsData.query.delete()

        # حفظ البيانات الجديدة (مشتركة لجميع المستخدمين)
        shifts_data = ShiftsData(
            user_id=current_user.id if current_user.is_authenticated else 1,
            shifts_data=json.dumps(data, ensure_ascii=False)
        )
        db.session.add(shifts_data)
        db.session.commit()

        print("✅ تم حفظ بيانات المناوبين بنجاح")
        return jsonify({'success': True, 'message': 'تم حفظ بيانات المناوبين بنجاح'})
    except Exception as e:
        print(f"❌ خطأ في حفظ بيانات المناوبين: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@receipts_bp.route('/api/get-shifts-data', methods=['GET'])
def get_shifts_data():
    """استرجاع بيانات كشف المناوبين من قاعدة البيانات"""
    try:
        # تحميل أحدث البيانات
        shifts_data = ShiftsData.query.order_by(ShiftsData.updated_at.desc()).first()

        if shifts_data:
            data = json.loads(shifts_data.shifts_data)
            print("✅ تم استرجاع بيانات المناوبين بنجاح")
            return jsonify({'success': True, 'shiftsData': data})
        else:
            print("ℹ️ لا توجد بيانات محفوظة للمناوبين")
            return jsonify({'success': False, 'message': 'لا توجد بيانات محفوظة'})
    except Exception as e:
        print(f"❌ خطأ في استرجاع بيانات المناوبين: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})


